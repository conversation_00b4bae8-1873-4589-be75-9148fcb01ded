const React = require('react')

module.exports = {
  ChatInput: jest.fn(() =>
    React.createElement('div', { 'data-testid': 'chat-input' }, 'ChatInput')
  ),
  ChatSubject: jest.fn(() =>
    React.createElement('div', { 'data-testid': 'chat-subject' }, 'ChatSubject')
  ),
  MessageHistoryHeader: jest.fn(() =>
    React.createElement(
      'div',
      { 'data-testid': 'message-history-header' },
      'MessageHistoryHeader'
    )
  ),
  OrderDetails: jest.fn(() =>
    React.createElement(
      'div',
      { 'data-testid': 'order-details' },
      'OrderDetails'
    )
  )
}
