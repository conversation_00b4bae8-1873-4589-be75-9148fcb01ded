const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './'
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  // Add more setup options before each test is run
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  // if using TypeScript with a baseUrl set to the root directory then you need the below for alias' to work
  moduleDirectories: ['node_modules', '<rootDir>/'],
  moduleNameMapper: {
    // Handle module aliases (this will be automatically configured for you soon)
    '^@components/(.*)$': '<rootDir>/components/$1',
    '^@pages/(.*)$': '<rootDir>/pages/$1',
    '^@styles/(.*)$': '<rootDir>/styles/$1',
    '^@core/(.*)$': '<rootDir>/core/$1',
    '^@modules/(.*)$': '<rootDir>/modules/$1',
    '^@shared/(.*)$': '<rootDir>/modules/shared/$1',
    '^@__mocks__/(.*)$': '<rootDir>/__mocks__/$1',
    '^@opentelemetry/semantic-conventions/incubating$':
      '@opentelemetry/semantic-conventions',
    '^@metromarkets/message-center-sdk$':
      '<rootDir>/__mocks__/message-center-sdk.js'
  },
  testEnvironment: 'jest-environment-jsdom',
  testMatch: ['**/+(*.)+(test).+(ts|tsx)'],
  coveragePathIgnorePatterns: ['/constants', '/classes'],
  collectCoverageFrom: [
    '**/**/*.{ts,tsx}',
    '!**/pages/**/*.tsx',
    '!@types/*',
    '!**/icons/**',
    '!**/shared/icons/**',
    '!**/*.stories.tsx',
    '!**/stories/**/*.tsx',
    '!**/__tests__/**/*.ts',
    '!**/__test__/**/*.ts',
    '!**/mocks/**/*.ts',
    '!**/Mocks/**.ts',
    '!**/mocks/**.ts',
    '!**/*mock*.ts',
    '!**/*Mock*.ts',
    '!**/cypress/**',
    '!**/styles/**',
    '!**/types/*.ts',
    '!jest.config.js',
    '!**/*.interface.ts',
    '!**/*.js',
    '!**/constants/*.ts',
    '!**/*constants*.ts',
    '!**/*.stories.*'
  ],
  collectCoverage: true,
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  },
  coverageReporters: ['text', 'text-summary', 'lcov']
}

process.env = Object.assign(process.env, {
  IDAM_SSO_ISSUER: 'IDAM_SSO_ISSUER-jest'
})

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
