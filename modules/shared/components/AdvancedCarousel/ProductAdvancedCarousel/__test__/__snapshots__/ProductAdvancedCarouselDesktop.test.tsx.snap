// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProductAdvancedCarouselDesktop renders correctly in desktop mode 1`] = `
<div>
  <div
    class="w-full 
    block
    relative
    pb-4
    md:pb-0
  "
  >
    <div
      data-testid="desktop-container"
    >
      <span
        class="
        left-0 
  flex 
  flex-row 
  flex-nowrap 
  justify-center 
  items-center 
  absolute 
  z-[5] 
  cursor-pointer 
  my-auto 
  mx-0 
  w-12
  bottom-0
  inset-y-0 
  bg-white-main
  text-primary-main
  h-12
  border-[1px]
  border-metro-blue-tint-80
  rounded-full
  hover:border-[2px]
  hover:border-primary-main
  hover:text-primary-main
  right-0
 ml-6"
        data-testid="carousel-arrow"
      >
        <svg
          aria-hidden="true"
          class="block align-middle"
          data-testid=""
          fill=""
          height="100%"
          viewBox="0 0 32 32"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <path
            d="M12.5 15.5C12.5 15.244 12.598 14.988 12.793 14.793L17.293 10.293C17.684 9.90201 18.316 9.90201 18.707 10.293C19.098 10.684 19.098 11.316 18.707 11.707L14.914 15.5L18.707 19.293C19.098 19.684 19.098 20.316 18.707 20.707C18.316 21.098 17.684 21.098 17.293 20.707L12.793 16.207C12.598 16.012 12.5 15.756 12.5 15.5"
            fill="currentColor"
            stroke="none"
          />
        </svg>
      </span>
      <span
        class="
        right-0 
  flex 
  flex-row 
  flex-nowrap 
  justify-center 
  items-center 
  absolute 
  z-[5] 
  cursor-pointer 
  my-auto 
  mx-0 
  w-12
  bottom-0
  inset-y-0 
  bg-white-main
  text-primary-main
  h-12
  border-[1px]
  border-metro-blue-tint-80
  rounded-full
  hover:border-[2px]
  hover:border-primary-main
  hover:text-primary-main
  right-0
 mr-6"
        data-testid="carousel-arrow"
      >
        <svg
          aria-hidden="true"
          class="block align-middle"
          data-testid=""
          fill=""
          height="100%"
          viewBox="0 0 32 32"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <path
            d="M19.5 16.5C19.5 16.756 19.402 17.012 19.207 17.207L14.707 21.707C14.316 22.098 13.684 22.098 13.293 21.707C12.902 21.316 12.902 20.684 13.293 20.293L17.086 16.5L13.293 12.707C12.902 12.316 12.902 11.684 13.293 11.293C13.684 10.902 14.316 10.902 14.707 11.293L19.207 15.793C19.402 15.988 19.5 16.244 19.5 16.5"
            fill="currentColor"
            stroke="none"
          />
        </svg>
      </span>
      <div
        class="
    flex
    w-full 
    overflow-x-scroll 
    relative 
    no-scrollbar
    max-w-[100vw] 
    my-0 
    mx-auto 
    gap-4  
    pb-4
    pr-4
   undefined"
      >
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/6f2c525f-32e5-4efb-a073-d790b5440648"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000241936"
                      >
                        <img
                          alt="METRO Professional Getränkekühlschrank GSC2360, Stahl/ Glas/ Kunststoff, 62 x 64.6 x 175.3 cm, 347 L, statische Lüfterkühlung, 180 W, mit Schloß, weiß"
                          src="https://prod-metro-markets.imgix.net/item_image/b32e9b46-7a61-4313-94e5-1ce1561f4003?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-METRO-Professional-Getränkekühlschrank-GSC2360,-Stahl/-Glas/-Kunststoff,-62-x-64.6-x-175.3-cm,-347-L,-statische-Lüfterkühlung,-180-W,-mit-Schloß,-weiß"
                    >
                      METRO Professional Getränkekühlschrank GSC2360, Stahl/ Glas/ Kunststoff, 62 x 64.6 x 175.3 cm, 347 L, statische Lüfterkühlung, 180 W, mit Schloß, weiß
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            499,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/e367342d-3bd4-43a1-83cf-ea7d6e4e7856"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000704483"
                      >
                        <img
                          alt="METRO Professional GEG2101 Griddleplatte, abnehmbare Auffangwanne, Überhitzungsschutz, Edelstahl, 55 x 41.7 x 24 cm, silber"
                          src="https://prod-metro-markets.imgix.net/item_image/0b5c0e0d-ac42-4372-a4b7-2bb8fbf3e018?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-METRO-Professional-GEG2101-Griddleplatte,-abnehmbare-Auffangwanne,-Überhitzungsschutz,-Edelstahl,-55-x-41.7-x-24-cm,-silber"
                    >
                      METRO Professional GEG2101 Griddleplatte, abnehmbare Auffangwanne, Überhitzungsschutz, Edelstahl, 55 x 41.7 x 24 cm, silber
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-metro-red"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                          <div
                            class="flex box-border min-w-[45.5px] h-[24px] rounded-sm bg-white-main border border-metro-red text-metro-red font-bold text-sm leading-[100%] items-center justify-center py-0 px-[6px] md:h-[24px]"
                            data-testid="discount-percentage"
                          >
                            <p
                              class="self-center"
                            >
                              - 
                              25
                              %
                            </p>
                          </div>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            149,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/a351fd4b-8a96-4b96-8534-56da64c02ed6"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000072600"
                      >
                        <img
                          alt="METRO Professional Geschirrspüler GDW4050, Edelstahl, 57 x 61 x 82 cm, 400 V, 30 Zyklen pro Stunde, 4900 W, silber"
                          src="https://prod-metro-markets.imgix.net/item_image/f25da0ab-b7fe-44b1-a0b1-b1a63868fabc?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-METRO-Professional-Geschirrspüler-GDW4050,-Edelstahl,-57-x-61-x-82-cm,-400-V,-30-Zyklen-pro-Stunde,-4900-W,-silber"
                    >
                      METRO Professional Geschirrspüler GDW4050, Edelstahl, 57 x 61 x 82 cm, 400 V, 30 Zyklen pro Stunde, 4900 W, silber
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] flex-wrap text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            1.199,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/522ddf53-936c-4a2f-9933-9f23e0489e93"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000228493"
                      >
                        <img
                          alt="Tarrington House Lounge Set Makasar, Akazienholz FSC/ Polyester, 2 Bänke, 1 Couchtisch, schwarz, 3 tlg."
                          src="https://prod-metro-markets.imgix.net/item_image/2b9863af-96f4-483c-8316-ce05af3f2902?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-Tarrington-House-Lounge-Set-Makasar,-Akazienholz-FSC/-Polyester,-2-Bänke,-1-Couchtisch,-schwarz,-3-tlg."
                    >
                      Tarrington House Lounge Set Makasar, Akazienholz FSC/ Polyester, 2 Bänke, 1 Couchtisch, schwarz, 3 tlg.
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            839,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/4bce4db1-3a1b-4914-b1c4-53cedc00c818"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000768803"
                      >
                        <img
                          alt="Tarrington House Mobile Klimaanlage MAC3550C, Kunststoff/Metall, 39.7x46.7x76.5cm, Temperaturber.: 17-30°C, Entfeuchtungsfunk., 12000BTU, 3500W, weiß"
                          src="https://prod-metro-markets.imgix.net/item_image/e083ab1e-494c-4d0f-a286-d31d41bd4af8?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-Tarrington-House-Mobile-Klimaanlage-MAC3550C,-Kunststoff/Metall,-39.7x46.7x76.5cm,-Temperaturber.:-17-30°C,-Entfeuchtungsfunk.,-12000BTU,-3500W,-weiß"
                    >
                      Tarrington House Mobile Klimaanlage MAC3550C, Kunststoff/Metall, 39.7x46.7x76.5cm, Temperaturber.: 17-30°C, Entfeuchtungsfunk., 12000BTU, 3500W, weiß
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            299,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/3a6ac106-32b7-48d5-99af-bece92b3aa9a"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000704369"
                      >
                        <img
                          alt="METRO Professional Getränkekühlschrank GSC2100, Stahl/Aluminium/Glas, 120 x 72.5 x 206.6 cm, 930L, Umluftkühlung, mit Rädern, Canopy und Schloß, weiß"
                          src="https://prod-metro-markets.imgix.net/item_image/d6e9fe7b-ed08-4ff2-b50d-7329dce4854e?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-METRO-Professional-Getränkekühlschrank-GSC2100,-Stahl/Aluminium/Glas,-120-x-72.5-x-206.6-cm,-930L,-Umluftkühlung,-mit-Rädern,-Canopy-und-Schloß,-weiß"
                    >
                      METRO Professional Getränkekühlschrank GSC2100, Stahl/Aluminium/Glas, 120 x 72.5 x 206.6 cm, 930L, Umluftkühlung, mit Rädern, Canopy und Schloß, weiß
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] flex-wrap text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            1.199,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/a6e44ca0-5552-4152-b437-4ffbf8124693"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000049144"
                      >
                        <img
                          alt="METRO Professional Gefrierschrank GFR1400, 940 l"
                          src="https://prod-metro-markets.imgix.net/item_image/ef198b62-d33f-4698-ae30-902995119d48?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-METRO-Professional-Gefrierschrank-GFR1400,-940-l"
                    >
                      METRO Professional Gefrierschrank GFR1400, 940 l
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] flex-wrap text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            1.999,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/f0d8d96e-859e-4ab7-bf36-ffd38818e9b8"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000241954"
                      >
                        <img
                          alt="Bosch Serie 2 WAJ280A0 Waschmaschine Freistehend Frontlader 7 kg 1400 RPM D Weiß"
                          src="https://prod-metro-markets.imgix.net/item_image/5c8d30a7-f4af-46e6-adca-fbf341a17659?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-Bosch-Serie-2-WAJ280A0-Waschmaschine-Freistehend-Frontlader-7-kg-1400-RPM-D-Weiß"
                    >
                      Bosch Serie 2 WAJ280A0 Waschmaschine Freistehend Frontlader 7 kg 1400 RPM D Weiß
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            349,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
        <div
          class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
          data-name="scrollableElement"
        >
          <a
            data-testid=""
            href="//product/f0d8d96e-859e-4ab7-bf36-ffd38818e9b8"
          >
            <div
              class="w-full h-full text-left"
            >
              <span>
                <article
                  class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                  data-testid="product_card"
                  test-target="product-card"
                >
                  <section
                    class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                  />
                  <div
                    class="flex-col"
                  >
                    <div
                      class="relative w-full"
                    >
                      <figure
                        class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                        test-target="product-card-AAA0000241954"
                      >
                        <img
                          alt="Bosch Serie 2 WAJ280A0 Waschmaschine Freistehend Frontlader 7 kg 1400 RPM D Weiß"
                          src="https://prod-metro-markets.imgix.net/item_image/5c8d30a7-f4af-46e6-adca-fbf341a17659?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                        />
                      </figure>
                    </div>
                    <section
                      test-target="product-cards-variants"
                    />
                  </div>
                  <div
                    class="flex flex-col undefined"
                  >
                    <p
                      class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                      data-testid="product-name"
                      test-target="product-card-Bosch-Serie-2-WAJ280A0-Waschmaschine-Freistehend-Frontlader-7-kg-1400-RPM-D-Weiß"
                    >
                      Bosch Serie 2 WAJ280A0 Waschmaschine Freistehend Frontlader 7 kg 1400 RPM D Weiß
                    </p>
                    <div
                      class="mt-auto"
                      data-testid="product-price-section"
                    >
                      <div
                        class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                      >
                        <div
                          class="min-h-5"
                          data-testid="strike-price-container"
                        />
                        <div
                          class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                        >
                          <p
                            class="leading-[22px] self-end"
                            data-testid="multi-unit-main-price"
                          >
                            499,99 €
                          </p>
                        </div>
                        <div
                          class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                          data-testid="units-count-container"
                        >
                          <span
                            data-testid="units-label"
                          >
                            3
                             
                            :
                          </span>
                          <span
                            data-testid="unit-price-container"
                          >
                             
                            349,99 €
                          </span>
                          3,00 €/3
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              </span>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;
