// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PercentageAdvancedCarousel should render 20% discount carousel in desktop 1`] = `
<div
  data-testid="desktop-container"
>
  <span
    class="
        left-0 
  flex 
  flex-row 
  flex-nowrap 
  justify-center 
  items-center 
  absolute 
  z-[5] 
  cursor-pointer 
  my-auto 
  mx-0 
  w-12
  bottom-0
  inset-y-0 
  bg-white-main
  text-primary-main
  h-12
  border-[1px]
  border-metro-blue-tint-80
  rounded-full
  hover:border-[2px]
  hover:border-primary-main
  hover:text-primary-main
  right-0
 ml-6"
    data-testid="carousel-arrow"
  >
    <svg
      aria-hidden="true"
      class="block align-middle"
      data-testid=""
      fill=""
      height="100%"
      viewBox="0 0 32 32"
      width="100%"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <path
        d="M12.5 15.5C12.5 15.244 12.598 14.988 12.793 14.793L17.293 10.293C17.684 9.90201 18.316 9.90201 18.707 10.293C19.098 10.684 19.098 11.316 18.707 11.707L14.914 15.5L18.707 19.293C19.098 19.684 19.098 20.316 18.707 20.707C18.316 21.098 17.684 21.098 17.293 20.707L12.793 16.207C12.598 16.012 12.5 15.756 12.5 15.5"
        fill="currentColor"
        stroke="none"
      />
    </svg>
  </span>
  <span
    class="
        right-0 
  flex 
  flex-row 
  flex-nowrap 
  justify-center 
  items-center 
  absolute 
  z-[5] 
  cursor-pointer 
  my-auto 
  mx-0 
  w-12
  bottom-0
  inset-y-0 
  bg-white-main
  text-primary-main
  h-12
  border-[1px]
  border-metro-blue-tint-80
  rounded-full
  hover:border-[2px]
  hover:border-primary-main
  hover:text-primary-main
  right-0
 mr-6"
    data-testid="carousel-arrow"
  >
    <svg
      aria-hidden="true"
      class="block align-middle"
      data-testid=""
      fill=""
      height="100%"
      viewBox="0 0 32 32"
      width="100%"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <path
        d="M19.5 16.5C19.5 16.756 19.402 17.012 19.207 17.207L14.707 21.707C14.316 22.098 13.684 22.098 13.293 21.707C12.902 21.316 12.902 20.684 13.293 20.293L17.086 16.5L13.293 12.707C12.902 12.316 12.902 11.684 13.293 11.293C13.684 10.902 14.316 10.902 14.707 11.293L19.207 15.793C19.402 15.988 19.5 16.244 19.5 16.5"
        fill="currentColor"
        stroke="none"
      />
    </svg>
  </span>
  <div
    class="
    flex
    w-full 
    overflow-x-scroll 
    relative 
    no-scrollbar
    max-w-[100vw] 
    my-0 
    mx-auto 
    gap-4  
    pb-4
    pr-4
   !pb-0"
  >
    <div
      class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
      data-name="scrollableElement"
    >
      <a
        data-testid=""
        href="https://marketplace-test.metro.de/marktplatz/product/f498e981-54a1-4b6c-9260-a6922e759abc?itemListName=Search results Page carousel|Deal with more than 20% discount"
      >
        <div
          class="w-full h-full text-left"
        >
          <span>
            <article
              class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
              data-testid="product_card"
              test-target="product-card"
            >
              <section
                class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
              />
              <div
                class="flex-col"
              >
                <div
                  class="relative w-full"
                >
                  <figure
                    class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                    test-target="product-card-AAA0000140729"
                  >
                    <img
                      alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück"
                      src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                    />
                  </figure>
                </div>
                <section
                  class="flex flex-col flex-col-reverse"
                  test-target="product-cards-variants"
                >
                  <p
                    class="text-center text-[14px] text-metro-blue-tint-20 hover:text-blue-main font-normal leading-6"
                    data-testid="more-sizes-text"
                    test-target="product-cards-more-available-sizes"
                  >
                    CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-AVAILABLE-SIZES
                  </p>
                </section>
              </div>
              <div
                class="flex flex-col justify-between flex-grow w-full"
              >
                <p
                  class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                  data-testid="product-name"
                  test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-12-Stück"
                >
                  Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück
                </p>
                <div
                  class="mt-auto"
                  data-testid="product-price-section"
                >
                  <div
                    class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                  >
                    <div
                      class="min-h-5"
                      data-testid="strike-price-container"
                    />
                    <div
                      class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                    >
                      <p
                        class="leading-[22px] self-end"
                        data-testid="single-unit-main-price"
                      >
                        8,61 €
                      </p>
                    </div>
                    <div
                      class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                      data-testid="units-count-container"
                    />
                  </div>
                </div>
                <button
                  aria-label="[object Object]"
                  class="w-fit"
                  data-state="closed"
                >
                  <div
                    aria-hidden="true"
                  >
                    <div
                      class="flex items-center relative cursor-default w-fit pt-2"
                    >
                      <div
                        class="mr-1.5 text-[14px] font-lato leading-5 text-[#677283] font-normal"
                      >
                        SHARED.SPONSORED.LABEL.TEXT
                      </div>
                      <div
                        class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-[14px] !h-[14px] cursor-default border-[1px] !border-[#677283] !bg-transparent before:!text-[#677283]"
                      />
                    </div>
                  </div>
                </button>
              </div>
            </article>
          </span>
        </div>
      </a>
    </div>
    <div
      class="basis-[230px] flex-grow-0 flex-shrink min-w-[230px] w-full relative box-border !h-auto"
      data-name="scrollableElement"
    >
      <a
        data-testid=""
        href="https://marketplace-test.metro.de/marktplatz/product/f498e981-54a1-4b6c-9260-a6922e759def?itemListName=Search results Page carousel|Deal with more than 20% discount"
      >
        <div
          class="w-full h-full text-left"
        >
          <span>
            <article
              class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
              data-testid="product_card"
              test-target="product-card"
            >
              <section
                class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
              />
              <div
                class="flex-col"
              >
                <div
                  class="relative w-full"
                >
                  <figure
                    class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                    test-target="product-card-AAA0000140729"
                  >
                    <img
                      alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück"
                      src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                    />
                  </figure>
                </div>
                <section
                  class="flex flex-col flex-col-reverse"
                  test-target="product-cards-variants"
                >
                  <p
                    class="text-center text-[14px] text-metro-blue-tint-20 hover:text-blue-main font-normal leading-6"
                    data-testid="more-sizes-text"
                    test-target="product-cards-more-available-sizes"
                  >
                    CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-AVAILABLE-SIZES
                  </p>
                </section>
              </div>
              <div
                class="flex flex-col undefined"
              >
                <p
                  class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                  data-testid="product-name"
                  test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-14-Stück"
                >
                  Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück
                </p>
                <div
                  class="mt-auto"
                  data-testid="product-price-section"
                >
                  <div
                    class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                  >
                    <div
                      class="min-h-5"
                      data-testid="strike-price-container"
                    />
                    <div
                      class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                    >
                      <p
                        class="leading-[22px] self-end"
                        data-testid="single-unit-main-price"
                      >
                        8,61 €
                      </p>
                    </div>
                    <div
                      class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                      data-testid="units-count-container"
                    />
                  </div>
                </div>
              </div>
            </article>
          </span>
        </div>
      </a>
    </div>
  </div>
</div>
`;

exports[`PercentageAdvancedCarousel should render 20% discount carousel in mobile 1`] = `
<div
  data-testid="mobile-container"
>
  <div
    class="flex gap-3 overflow-auto max-w-[100vw] mx-auto pr-4 undefined"
    data-testid="mobileScroll"
  >
    <div
      data-testid="mobile-swiper-slider"
      test-target="product-carousel-item-mobile"
    >
      <a
        data-testid=""
        href="https://marketplace-test.metro.de/marktplatz/product/f498e981-54a1-4b6c-9260-a6922e759abc?itemListName=Search results Page carousel|Deal with more than 20% discount"
      >
        <div
          class="w-full h-full text-left"
        >
          <span>
            <article
              class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
              data-testid="product_card"
              test-target="product-card"
            >
              <section
                class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
              />
              <div
                class="flex-col"
              >
                <div
                  class="relative w-full"
                >
                  <figure
                    class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                    test-target="product-card-AAA0000140729"
                  >
                    <img
                      alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück"
                      src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                    />
                  </figure>
                </div>
                <section
                  test-target="product-cards-variants"
                >
                  <p
                    class="text-center text-[13px] leading-[23px] text-metro-blue-tint-20 font-normal"
                    data-testid="only-sizes-text"
                  >
                     CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-SIZES
                  </p>
                </section>
              </div>
              <div
                class="flex flex-col justify-between flex-grow w-full"
              >
                <p
                  class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                  data-testid="product-name"
                  test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-12-Stück"
                >
                  Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück
                </p>
                <div
                  class="mt-auto"
                  data-testid="product-price-section"
                >
                  <div
                    class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                  >
                    <div
                      class="min-h-5"
                      data-testid="strike-price-container"
                    />
                    <div
                      class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                    >
                      <p
                        class="leading-[22px] self-end"
                        data-testid="single-unit-main-price"
                      >
                        8,61 €
                      </p>
                    </div>
                    <div
                      class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                      data-testid="units-count-container"
                    />
                  </div>
                </div>
                <div>
                  <span>
                    <div
                      class="flex items-center relative pt-2"
                    >
                      <div
                        class="mr-1.5 text-[14px] font-lato leading-5 text-[#677283] font-normal"
                      >
                        SHARED.SPONSORED.LABEL.TEXT
                      </div>
                      <div
                        class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-4 !h-4 border-[1px] !border-[#677283] !bg-transparent before:!text-[#677283] font-normal"
                      />
                    </div>
                  </span>
                </div>
              </div>
            </article>
          </span>
        </div>
      </a>
    </div>
    <div
      data-testid="mobile-swiper-slider"
      test-target="product-carousel-item-mobile"
    >
      <a
        data-testid=""
        href="https://marketplace-test.metro.de/marktplatz/product/f498e981-54a1-4b6c-9260-a6922e759def?itemListName=Search results Page carousel|Deal with more than 20% discount"
      >
        <div
          class="w-full h-full text-left"
        >
          <span>
            <article
              class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
              data-testid="product_card"
              test-target="product-card"
            >
              <section
                class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
              />
              <div
                class="flex-col"
              >
                <div
                  class="relative w-full"
                >
                  <figure
                    class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                    test-target="product-card-AAA0000140729"
                  >
                    <img
                      alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück"
                      src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                    />
                  </figure>
                </div>
                <section
                  test-target="product-cards-variants"
                >
                  <p
                    class="text-center text-[13px] leading-[23px] text-metro-blue-tint-20 font-normal"
                    data-testid="only-sizes-text"
                  >
                     CATALOG.CATALOG_PAGE.PRODUCT-CARD.VARIANTS-PLUS-SIZES
                  </p>
                </section>
              </div>
              <div
                class="flex flex-col undefined"
              >
                <p
                  class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                  data-testid="product-name"
                  test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-14-Stück"
                >
                  Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück
                </p>
                <div
                  class="mt-auto"
                  data-testid="product-price-section"
                >
                  <div
                    class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                  >
                    <div
                      class="min-h-5"
                      data-testid="strike-price-container"
                    />
                    <div
                      class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                    >
                      <p
                        class="leading-[22px] self-end"
                        data-testid="single-unit-main-price"
                      >
                        8,61 €
                      </p>
                    </div>
                    <div
                      class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                      data-testid="units-count-container"
                    />
                  </div>
                </div>
              </div>
            </article>
          </span>
        </div>
      </a>
    </div>
  </div>
</div>
`;
