// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ZeroResults should render alternative products carousel successfully if recommended products is present 1`] = `
<div>
  <div
    class="pt-2"
    data-testid="zero-result-page"
  >
    <div
      class="w-full h-[287px] bg-center bg-cover bg-no-repeat items-center px-4 py-6 flex flex-col md:bg-120 bg-[url:var(--image-mobile)] bg-[position:var(--image-position-mobile)]  md:bg-[url:var(--image-table)] md:bg-[position:var(--image-position-big)] lg:bg-[url:var(--image-desktop)]"
      style="--image-mobile: url(https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/search-no-result/background_zero_results_mobile.webp); --image-table: url(https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/search-no-result/background_zero_results_tablet.webp); --image-desktop: url(https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/search-no-result/background_zero_results.webp); --image-position-mobile: 70% 130%; --image-position-big: 60% 60%;"
    >
      <svg
        aria-hidden="true"
        class=""
        data-testid=""
        fill=""
        height="63px"
        viewBox="0 0 27 25"
        width="63px"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <svg
          fill="none"
          height="full"
          viewBox="0 0 86 87"
          width="full"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M85.8516 31.5348C85.8516 14.1186 71.6031 0 54.0266 0C36.4502 0 22.2017 14.1186 22.2017 31.5348C22.2017 48.9509 36.4502 63.0695 54.0266 63.0695C71.6031 63.0695 85.8516 48.9509 85.8516 31.5348ZM27.8842 31.5344C27.8842 17.2283 39.5883 5.63086 54.0261 5.63086C68.4638 5.63086 80.168 17.2283 80.168 31.5344C80.168 45.8406 68.4638 57.438 54.0261 57.438C39.5883 57.438 27.8842 45.8406 27.8842 31.5344Z"
            fill="url(#paint0_linear_121_38090)"
            fill-rule="evenodd"
          />
          <path
            d="M54.0261 5.63086C39.5883 5.63086 27.8842 17.2283 27.8842 31.5344C27.8842 45.8406 39.5883 57.438 54.0261 57.438C68.4638 57.438 80.168 45.8406 80.168 31.5344C80.168 17.2283 68.4638 5.63086 54.0261 5.63086Z"
            fill="white"
          />
          <path
            d="M34.9543 52.1203C33.3527 50.5477 30.7799 50.5248 29.15 52.0473L29.0605 52.1335L1.21367 79.7264C-0.410143 81.3354 -0.404131 83.9382 1.2271 85.5399C2.82867 87.1125 5.40145 87.1354 7.03132 85.6129L7.12089 85.5267L34.9677 57.9338C36.5915 56.3248 36.5855 53.722 34.9543 52.1203Z"
            fill="url(#paint1_linear_121_38090)"
          />
          <rect
            fill="#FFED00"
            height="6.85528"
            transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 44.4629 45.2866)"
            width="32.2462"
          />
          <rect
            fill="#FFED00"
            height="6.85463"
            transform="matrix(0.707107 0.707107 0.707107 -0.707107 39.541 22.4102)"
            width="32.2462"
          />
          <defs>
            <lineargradient
              gradientUnits="userSpaceOnUse"
              id="paint0_linear_121_38090"
              x1="7.15769"
              x2="123.401"
              y1="-100.448"
              y2="-7.45462"
            >
              <stop
                stop-color="#6C80AF"
              />
              <stop
                offset="1"
                stop-color="#3A4977"
              />
            </lineargradient>
            <lineargradient
              gradientUnits="userSpaceOnUse"
              id="paint1_linear_121_38090"
              x1="13.1537"
              x2="22.5562"
              y1="59.3216"
              y2="68.442"
            >
              <stop
                stop-color="#495B8E"
              />
              <stop
                offset="1"
                stop-color="#232E55"
              />
            </lineargradient>
          </defs>
        </svg>
      </svg>
      <div
        class="font-lato text-left md:text-center"
      >
        <p
          class="text-primary-main text-[20px] leading-6 font-extrabold my-4"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.MAIN_TITLE
          ""
        </p>
        <p
          class="text-primary-main text-regular font-bold leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.TRIED_THESE
        </p>
        <p
          class="text-primary-tint-30 text-regular font-normal leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.SPELLING
        </p>
        <p
          class="text-primary-tint-30 text-regular font-normal leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.GENERAL_TERM
        </p>
        <p
          class="text-primary-tint-30 text-regular font-normal leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.SYNONYMS
        </p>
      </div>
    </div>
    <div />
    <div>
      <div
        class="bg-white-main px-6 pt-6 pb-4 mb-6 xs-md:p-0"
        data-testid="alternative-product-carousel"
      >
        <div
          class="flex justify-between"
        >
          <div
            class="font-lato pb-3 text-[20px] m-0 font-extrabold text-metro-blue-main xs-md:p-4 antialiased leading-6"
          >
            CATALOG.CATALOG_PAGE.ZERO_RESULT.ALTERNATIVE_PRODUCTS.TITLE
          </div>
        </div>
        <div
          class="w-full 
    block
    relative
    pb-4
    md:pb-0
  "
        >
          <div
            data-testid="mobile-container"
          >
            <div
              class="flex gap-3 overflow-auto max-w-[100vw] mx-auto pr-4 undefined"
              data-testid="mobileScroll"
            >
              <div
                data-testid="mobile-swiper-slider"
                test-target="product-carousel-item-mobile"
              >
                <a
                  data-testid=""
                  href="https://marketplace-test.metro.de/marktplatz/product/3f6732f0-60fc-4da5-be09-800c6f0d8745?itemListName=alternative products/Search Zero Result"
                >
                  <div
                    class="w-full h-full text-left"
                  >
                    <span>
                      <article
                        class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                        data-testid="product_card"
                        test-target="product-card"
                      >
                        <section
                          class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                        >
                          <div
                            class="prose antialiased font-lato text-[13px] font-bold h-24px bg-metro-blue-tint-95 px-8px leading-6 w-fit text-metro-blue-main rounded-bl-sm rounded-br-sm"
                            data-testid="serie-label"
                          >
                            CATALOG.CATALOG_PAGE.PRODUCT-CARD.SERIES
                          </div>
                        </section>
                        <div
                          class="flex-col"
                        >
                          <div
                            class="relative w-full"
                          >
                            <figure
                              class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                              test-target="product-card-AAA0045986465"
                            >
                              <img
                                alt="METRO Professional Bordeauxglas Carré, Glas, 53 cl, geeicht, 6 Stück"
                                src="https://prod-metro-markets.imgix.net/item_image/1b8b3d5b-ea06-4026-8c2d-871023e08ee7?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                              />
                            </figure>
                          </div>
                          <section
                            test-target="product-cards-variants"
                          />
                        </div>
                        <div
                          class="flex flex-col undefined"
                        >
                          <p
                            class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                            data-testid="product-name"
                            test-target="product-card-METRO-Professional-Bordeauxglas-Carré,-Glas,-53-cl,-geeicht,-6-Stück"
                          >
                            METRO Professional Bordeauxglas Carré, Glas, 53 cl, geeicht, 6 Stück
                          </p>
                          <div
                            class="mt-auto"
                            data-testid="product-price-section"
                          >
                            <div
                              class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                            >
                              <div
                                class="min-h-5"
                                data-testid="strike-price-container"
                              />
                              <div
                                class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                              >
                                <p
                                  class="leading-[22px] self-end"
                                  data-testid="multi-unit-main-price"
                                >
                                  1,31 €
                                </p>
                                <span
                                  class="text-[14px] font-normal leading-[22px] text-[#677283]"
                                  data-testid="unit-price-separator"
                                >
                                  /
                                  Stück
                                </span>
                              </div>
                              <div
                                class="flex gap-1 text-[14px] font-normal text-[#677283] min-h-[20px]"
                                data-testid="units-count-container"
                              >
                                <span
                                  data-testid="units-label"
                                >
                                  6
                                   
                                  Stück
                                  :
                                </span>
                                <span
                                  data-testid="unit-price-container"
                                >
                                   
                                  7,88 €
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </article>
                    </span>
                  </div>
                </a>
              </div>
              <div
                data-testid="mobile-swiper-slider"
                test-target="product-carousel-item-mobile"
              >
                <a
                  data-testid=""
                  href="https://marketplace-test.metro.de/marktplatz/product/0be111e0-a65a-43af-8eb2-2be7a71254c4?itemListName=alternative products/Search Zero Result"
                >
                  <div
                    class="w-full h-full text-left"
                  >
                    <span>
                      <article
                        class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                        data-testid="product_card"
                        test-target="product-card"
                      >
                        <section
                          class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                        />
                        <div
                          class="flex-col"
                        >
                          <div
                            class="relative w-full"
                          >
                            <figure
                              class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                              test-target="product-card-AAA0045386496"
                            >
                              <img
                                alt="aro Weizenmehl Typ 405 10 x 1 kg (10 kg)"
                                src="https://prod-metro-markets.imgix.net/item_image/930602de-2576-4945-a652-e2182630a604?h=148&ixlib=php-2.3.0&q=100&w=148&auto=format,compress"
                              />
                            </figure>
                          </div>
                          <section
                            test-target="product-cards-variants"
                          />
                        </div>
                        <div
                          class="flex flex-col undefined"
                        >
                          <p
                            class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                            data-testid="product-name"
                            test-target="product-card-aro-Weizenmehl-Typ-405-10-x-1-kg-(10-kg)"
                          >
                            aro Weizenmehl Typ 405 10 x 1 kg (10 kg)
                          </p>
                          <div
                            class="mt-auto"
                            data-testid="product-price-section"
                          >
                            <div
                              class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                            >
                              <div
                                class="min-h-5"
                                data-testid="strike-price-container"
                              />
                              <div
                                class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                              >
                                <p
                                  class="leading-[22px] self-end"
                                  data-testid="single-unit-main-price"
                                >
                                  6,10 €
                                </p>
                              </div>
                              <div
                                class="flex gap-1 text-[14px] font-normal text-[#677283] min-h-[20px]"
                                data-testid="units-count-container"
                              >
                                0,61 €/kg
                              </div>
                            </div>
                          </div>
                        </div>
                      </article>
                    </span>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-full xl:max-w-xl mx-auto p-6 pb-4 mb-6 bg-white-main"
    >
      <div
        class="px-4 py-4 md:py-6"
      >
        <h3
          class="mb-5 text-blue-shade-60 font-extrabold font-lato text-regular md:text-[20px]"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.CATEGORIES_TITLE
        </h3>
        <div
          data-testid="desktop-container"
        >
          <div
            class="flex overflow-auto max-w-[95vw] my-0 mx-auto gap-6"
            data-testid="mobileScroll"
          >
            <a
              class="
        group
        no-underline 
        cursor-pointer 
        flex 
        flex-col 
        items-center
        sm:w-[50%]
        md:w-[145px]
        lg:w-[157px]
        xl:w-[10%]
        no-tap-highlight
    "
              href="https://marketplace-test.metro.de/marktplatz/c/mc1"
            >
              <div
                class="
        flex 
        items-center 
        justify-center 
        w-[80px] 
        h-[80px] 
        border-[2px] 
        border-blue-tint-80 
        rounded-[50%] 
        mb-2 
        group-hover:border-[2px] 
        group-hover:border-blue-main
    "
              >
                <figure
                  class="
        m-0
    "
                />
              </div>
              <span
                class="
        font-lato
        text-metro-blue-main 
        text-[13px]
        font-extrabold 
        max-w-[115px]
        pb-[20px]
        break-words 
        hyphens-auto 
        text-center 
        group-hover:text-blue-main
        leading-4
    "
              >
                MockCategory1
              </span>
            </a>
            <a
              class="
        group
        no-underline 
        cursor-pointer 
        flex 
        flex-col 
        items-center
        sm:w-[50%]
        md:w-[145px]
        lg:w-[157px]
        xl:w-[10%]
        no-tap-highlight
    "
              href="https://marketplace-test.metro.de/marktplatz/c/mc2"
            >
              <div
                class="
        flex 
        items-center 
        justify-center 
        w-[80px] 
        h-[80px] 
        border-[2px] 
        border-blue-tint-80 
        rounded-[50%] 
        mb-2 
        group-hover:border-[2px] 
        group-hover:border-blue-main
    "
              >
                <figure
                  class="
        m-0
    "
                />
              </div>
              <span
                class="
        font-lato
        text-metro-blue-main 
        text-[13px]
        font-extrabold 
        max-w-[115px]
        pb-[20px]
        break-words 
        hyphens-auto 
        text-center 
        group-hover:text-blue-main
        leading-4
    "
              >
                MockCategory2
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-full xl:max-w-xl mx-auto"
    />
  </div>
</div>
`;

exports[`ZeroResults should render successfully 1`] = `
<div>
  <div
    class="pt-2"
    data-testid="zero-result-page"
  >
    <div
      class="w-full h-[287px] bg-center bg-cover bg-no-repeat items-center px-4 py-6 flex flex-col md:bg-120 bg-[url:var(--image-mobile)] bg-[position:var(--image-position-mobile)]  md:bg-[url:var(--image-table)] md:bg-[position:var(--image-position-big)] lg:bg-[url:var(--image-desktop)]"
      style="--image-mobile: url(https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/search-no-result/background_zero_results_mobile.webp); --image-table: url(https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/search-no-result/background_zero_results_tablet.webp); --image-desktop: url(https://staging-cdn-bucket.pp-de.metro-marketplace.cloud/images/search-no-result/background_zero_results.webp); --image-position-mobile: 70% 130%; --image-position-big: 60% 60%;"
    >
      <svg
        aria-hidden="true"
        class=""
        data-testid=""
        fill=""
        height="63px"
        viewBox="0 0 27 25"
        width="63px"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <svg
          fill="none"
          height="full"
          viewBox="0 0 86 87"
          width="full"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M85.8516 31.5348C85.8516 14.1186 71.6031 0 54.0266 0C36.4502 0 22.2017 14.1186 22.2017 31.5348C22.2017 48.9509 36.4502 63.0695 54.0266 63.0695C71.6031 63.0695 85.8516 48.9509 85.8516 31.5348ZM27.8842 31.5344C27.8842 17.2283 39.5883 5.63086 54.0261 5.63086C68.4638 5.63086 80.168 17.2283 80.168 31.5344C80.168 45.8406 68.4638 57.438 54.0261 57.438C39.5883 57.438 27.8842 45.8406 27.8842 31.5344Z"
            fill="url(#paint0_linear_121_38090)"
            fill-rule="evenodd"
          />
          <path
            d="M54.0261 5.63086C39.5883 5.63086 27.8842 17.2283 27.8842 31.5344C27.8842 45.8406 39.5883 57.438 54.0261 57.438C68.4638 57.438 80.168 45.8406 80.168 31.5344C80.168 17.2283 68.4638 5.63086 54.0261 5.63086Z"
            fill="white"
          />
          <path
            d="M34.9543 52.1203C33.3527 50.5477 30.7799 50.5248 29.15 52.0473L29.0605 52.1335L1.21367 79.7264C-0.410143 81.3354 -0.404131 83.9382 1.2271 85.5399C2.82867 87.1125 5.40145 87.1354 7.03132 85.6129L7.12089 85.5267L34.9677 57.9338C36.5915 56.3248 36.5855 53.722 34.9543 52.1203Z"
            fill="url(#paint1_linear_121_38090)"
          />
          <rect
            fill="#FFED00"
            height="6.85528"
            transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 44.4629 45.2866)"
            width="32.2462"
          />
          <rect
            fill="#FFED00"
            height="6.85463"
            transform="matrix(0.707107 0.707107 0.707107 -0.707107 39.541 22.4102)"
            width="32.2462"
          />
          <defs>
            <lineargradient
              gradientUnits="userSpaceOnUse"
              id="paint0_linear_121_38090"
              x1="7.15769"
              x2="123.401"
              y1="-100.448"
              y2="-7.45462"
            >
              <stop
                stop-color="#6C80AF"
              />
              <stop
                offset="1"
                stop-color="#3A4977"
              />
            </lineargradient>
            <lineargradient
              gradientUnits="userSpaceOnUse"
              id="paint1_linear_121_38090"
              x1="13.1537"
              x2="22.5562"
              y1="59.3216"
              y2="68.442"
            >
              <stop
                stop-color="#495B8E"
              />
              <stop
                offset="1"
                stop-color="#232E55"
              />
            </lineargradient>
          </defs>
        </svg>
      </svg>
      <div
        class="font-lato text-left md:text-center"
      >
        <p
          class="text-primary-main text-[20px] leading-6 font-extrabold my-4"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.MAIN_TITLE
          ""
        </p>
        <p
          class="text-primary-main text-regular font-bold leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.TRIED_THESE
        </p>
        <p
          class="text-primary-tint-30 text-regular font-normal leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.SEARCH.SPELLING
        </p>
        <p
          class="text-primary-tint-30 text-regular font-normal leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.GENERAL_TERM
        </p>
        <p
          class="text-primary-tint-30 text-regular font-normal leading-6"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.SYNONYMS
        </p>
      </div>
    </div>
    <div />
    <div
      class="w-full xl:max-w-xl mx-auto p-6 pb-4 mb-6 bg-white-main"
    >
      <div
        class="px-4 py-4 md:py-6"
      >
        <h3
          class="mb-5 text-blue-shade-60 font-extrabold font-lato text-regular md:text-[20px]"
        >
          CATALOG.CATALOG_PAGE.ZERO_RESULT.CATEGORIES_TITLE
        </h3>
        <div
          data-testid="desktop-container"
        >
          <div
            class="flex overflow-auto max-w-[95vw] my-0 mx-auto gap-6"
            data-testid="mobileScroll"
          >
            <a
              class="
        group
        no-underline 
        cursor-pointer 
        flex 
        flex-col 
        items-center
        sm:w-[50%]
        md:w-[145px]
        lg:w-[157px]
        xl:w-[10%]
        no-tap-highlight
    "
              href="https://marketplace-test.metro.de/marktplatz/c/mc1"
            >
              <div
                class="
        flex 
        items-center 
        justify-center 
        w-[80px] 
        h-[80px] 
        border-[2px] 
        border-blue-tint-80 
        rounded-[50%] 
        mb-2 
        group-hover:border-[2px] 
        group-hover:border-blue-main
    "
              >
                <figure
                  class="
        m-0
    "
                />
              </div>
              <span
                class="
        font-lato
        text-metro-blue-main 
        text-[13px]
        font-extrabold 
        max-w-[115px]
        pb-[20px]
        break-words 
        hyphens-auto 
        text-center 
        group-hover:text-blue-main
        leading-4
    "
              >
                MockCategory1
              </span>
            </a>
            <a
              class="
        group
        no-underline 
        cursor-pointer 
        flex 
        flex-col 
        items-center
        sm:w-[50%]
        md:w-[145px]
        lg:w-[157px]
        xl:w-[10%]
        no-tap-highlight
    "
              href="https://marketplace-test.metro.de/marktplatz/c/mc2"
            >
              <div
                class="
        flex 
        items-center 
        justify-center 
        w-[80px] 
        h-[80px] 
        border-[2px] 
        border-blue-tint-80 
        rounded-[50%] 
        mb-2 
        group-hover:border-[2px] 
        group-hover:border-blue-main
    "
              >
                <figure
                  class="
        m-0
    "
                />
              </div>
              <span
                class="
        font-lato
        text-metro-blue-main 
        text-[13px]
        font-extrabold 
        max-w-[115px]
        pb-[20px]
        break-words 
        hyphens-auto 
        text-center 
        group-hover:text-blue-main
        leading-4
    "
              >
                MockCategory2
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-full xl:max-w-xl mx-auto"
    />
  </div>
</div>
`;
