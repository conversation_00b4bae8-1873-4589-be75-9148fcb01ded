// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PLPGRID should create the PLP carousels grid for ai search 1`] = `
<div
  class="mt-2"
  data-testid="ai-plp-grid"
>
  <div
    data-testid="ai-carousel-0"
  >
    <div
      class="p-0 rounded-xs xs-lgMinus:relative xs-lgMinus:min-w-[100vw] xs-lgMinus:left-[-16px] mt-6 bg-white-main"
      data-testid="ai-advanced-carousel"
    >
      <div
        class="bg-white-main  pl-4 md:pl-0"
        data-testid="textbox-carousel-wrapper"
      >
        <div
          class="flex flex-col md:flex-row"
        >
          <div
            class="flex w-full md:w-[35%] lg:w-[30%] xl:w-[25%] py-4 md:py-6 md:pl-10 md:pr-6 md:gap-2 "
            data-testid="textbox-carousel-header-wrapper"
          >
            <header
              class="flex md:flex-col md:justify-center flex-col gap-2"
            >
              <div
                class="md:max-w-none flex flex-col gap-1 
          mb-0"
              >
                <div
                  class="flex gap-1 font-metro-ca-800 text-metro-blue-tint-20"
                >
                  <svg
                    aria-hidden="true"
                    class="block align-middle"
                    data-testid="svg-icon"
                    fill=""
                    height="100%"
                    viewBox="0 0 27 25"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                  >
                    <svg
                      fill="none"
                      height="100%"
                      viewBox="0 0 22 18"
                      width="100%"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        id="Group 2095"
                      >
                        <path
                          d="M7.82224 6.10618C7.70118 5.6673 7.30933 5.36719 6.86333 5.36719C6.41733 5.36719 6.0223 5.67053 5.90442 6.10618C5.0602 9.20414 4.56642 9.70433 1.50492 10.5595C1.07166 10.6821 0.775391 11.0791 0.775391 11.5308C0.775391 11.9826 1.07485 12.3828 1.50492 12.5022C4.56323 13.3573 5.05702 13.8575 5.90442 16.9587C6.02548 17.3976 6.41733 17.6977 6.86333 17.6977C7.30933 17.6977 7.70436 17.3944 7.82224 16.9587C8.66646 13.8608 9.16025 13.3606 12.2217 12.5022C12.655 12.3796 12.9513 11.9826 12.9513 11.5308C12.9513 11.0791 12.6518 10.6789 12.2217 10.5595C9.16343 9.70433 8.66964 9.20414 7.82224 6.10618ZM6.86014 13.764C6.33131 12.7442 5.66549 12.0698 4.6588 11.5341C5.66549 10.9984 6.33131 10.3239 6.86014 9.30418C7.38898 10.3239 8.05479 10.9984 9.06149 11.5341C8.05479 12.0698 7.38898 12.7442 6.86014 13.764Z"
                          fill="#F3CD0B"
                          id="Vector"
                        />
                        <path
                          d="M4.6582 11.5346C5.6649 12.0703 6.33071 12.7447 6.85955 13.7645C7.38838 12.7447 8.0542 12.0703 9.06089 11.5346C8.0542 10.9989 7.38838 10.3244 6.85955 9.30469C6.33071 10.3244 5.6649 10.9989 4.6582 11.5346Z"
                          fill="#FFED00"
                          id="Vector_2"
                        />
                        <path
                          d="M20.4946 5.017C18.0129 4.32319 17.6497 3.95531 16.9616 1.43821C16.8405 0.999334 16.4487 0.699219 16.0027 0.699219C15.5567 0.699219 15.1616 1.00256 15.0438 1.43821C14.3588 3.95208 13.9957 4.31996 11.5108 5.017C11.0775 5.13963 10.7812 5.53656 10.7812 5.98834C10.7812 6.44013 11.0807 6.84028 11.5108 6.95968C13.9925 7.6535 14.3556 8.02138 15.0438 10.5385C15.1648 10.9774 15.5567 11.2775 16.0027 11.2775C16.4487 11.2775 16.8437 10.9741 16.9616 10.5385C17.6465 8.02461 18.0097 7.65673 20.4946 6.95968C20.9278 6.83706 21.2241 6.44013 21.2241 5.98834C21.2241 5.53656 20.9246 5.1364 20.4946 5.017ZM16.0027 7.47924C15.6268 6.84028 15.1648 6.36913 14.5309 5.98834C15.1616 5.60755 15.6268 5.13963 16.0027 4.49745C16.3786 5.1364 16.8405 5.60755 17.4745 5.98834C16.8437 6.36913 16.3786 6.83706 16.0027 7.47924Z"
                          fill="#F3CD0B"
                          id="Vector_3"
                        />
                        <path
                          d="M14.5293 5.98699C15.1633 6.36778 15.6252 6.83893 16.0011 7.47788C16.377 6.8357 16.8421 6.36778 17.4729 5.98699C16.839 5.6062 16.377 5.13505 16.0011 4.49609C15.6252 5.13828 15.1601 5.6062 14.5293 5.98699Z"
                          fill="#FFED00"
                          id="Vector_4"
                        />
                      </g>
                    </svg>
                  </svg>
                  <p>
                    CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.AI_EXPLORER
                  </p>
                  <svg
                    aria-hidden="true"
                    class="mt-1"
                    data-testid=""
                    fill=""
                    height="20px"
                    viewBox="0 0 27 25"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                  >
                    <g
                      fill="#33578E"
                      id="icon-/-alert-info"
                      stroke="none"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M0 9.99939C0 4.48566 4.48539 0 10 0C15.5146 0 20 4.48566 20 9.99939C20 15.5143 15.5146 20 10 20C4.48539 20 0 15.5143 0 9.99939ZM1.8184 9.99939C1.8184 14.5117 5.48794 18.1815 10 18.1815C14.5108 18.1815 18.1816 14.5117 18.1816 9.99939C18.1816 5.48827 14.5108 1.81851 10 1.81851C5.48794 1.81851 1.8184 5.48827 1.8184 9.99939ZM8.675 5.69766C8.675 4.96783 9.2678 4.375 9.99758 4.375C10.7262 4.375 11.3202 4.96783 11.3202 5.69766C11.3202 6.42749 10.7262 7.02033 9.99758 7.02033C9.2678 7.02033 8.675 6.42749 8.675 5.69766ZM10.8865 15.7859V15.0231V14.3996V13.9674V8.98099C10.8865 8.48295 10.4854 8.07802 9.98888 8.0718C9.99366 8.07176 9.99846 8.07173 10.0033 8.07173H9.97728C9.98115 8.07173 9.98502 8.07176 9.98888 8.0718C9.3735 8.07802 9.06738 8.48295 9.06738 8.98099C9.06738 9.23008 9.06699 9.27667 9.06659 9.32361C9.06619 9.37125 9.06578 9.41925 9.06578 9.67971L9.06808 9.89024V13.9674V14.3217V14.8553V15.7859H10.8865Z"
                        fill="currentColor"
                        fill-rule="evenodd"
                        id="alert-info"
                      />
                    </g>
                  </svg>
                </div>
                <h1
                  class="font-extrabold text-metro-blue-main font-metro-ca uppercase text-[20px] md:text-2xl"
                  data-testid="title"
                >
                  hotel description
                </h1>
              </div>
            </header>
          </div>
          <div
            class="block relative md:w-[65%] lg:w-[70%] xl:w-[75%] md:pt-4"
          >
            <div
              class="w-full 
    block
    relative
    pb-4
    md:pb-0
  "
            >
              <div
                data-testid="mobile-container"
              >
                <div
                  class="flex gap-3 overflow-auto max-w-[100vw] mx-auto pr-4 undefined"
                  data-testid="mobileScroll"
                >
                  <div
                    data-testid="mobile-swiper-slider"
                    test-target="product-carousel-item-mobile"
                  >
                    <a
                      class=""
                      data-testid=""
                      href="https://marketplace-test.makro.es/marketplace/product/f498e981-54a1-4b6c-9260-a6922e759abc"
                      test-target=""
                    >
                      <div
                        class="w-full h-full text-left"
                      >
                        <span>
                          <article
                            class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                            data-testid="product_card"
                            test-target="product-card"
                          >
                            <section
                              class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                            />
                            <div
                              class="flex-col"
                            >
                              <div
                                class="relative w-full"
                              >
                                <figure
                                  class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                                  test-target="product-card-AAA0000140729"
                                >
                                  <img
                                    alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück"
                                    src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                                  />
                                </figure>
                              </div>
                              <section
                                test-target="product-cards-variants"
                              >
                                <div
                                  class="min-h-[24px]"
                                  data-testid="empty-container"
                                />
                              </section>
                            </div>
                            <div
                              class="flex flex-col justify-between flex-grow w-full"
                            >
                              <p
                                class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                                data-testid="product-name"
                                test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-12-Stück"
                              >
                                Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück
                              </p>
                              <div
                                class="mt-auto"
                                data-testid="product-price-section"
                              >
                                <div
                                  class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                                >
                                  <div
                                    class="min-h-5"
                                    data-testid="strike-price-container"
                                  />
                                  <div
                                    class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                                  >
                                    <p
                                      class="leading-[22px] self-end"
                                      data-testid="single-unit-main-price"
                                    >
                                      8,61 €
                                    </p>
                                  </div>
                                  <div
                                    class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                                    data-testid="units-count-container"
                                  />
                                </div>
                              </div>
                              <div>
                                <span>
                                  <div
                                    class="flex items-center relative pt-2"
                                  >
                                    <div
                                      class="mr-1.5 text-[14px] font-lato leading-5 text-[#677283] font-normal"
                                    >
                                      SHARED.SPONSORED.LABEL.TEXT
                                    </div>
                                    <div
                                      class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-4 !h-4 border-[1px] !border-[#677283] !bg-transparent before:!text-[#677283] font-normal"
                                    />
                                  </div>
                                </span>
                              </div>
                            </div>
                          </article>
                        </span>
                      </div>
                    </a>
                  </div>
                  <div
                    data-testid="mobile-swiper-slider"
                    test-target="product-carousel-item-mobile"
                  >
                    <a
                      class=""
                      data-testid=""
                      href="https://marketplace-test.makro.es/marketplace/product/f498e981-54a1-4b6c-9260-a6922e759def"
                      test-target=""
                    >
                      <div
                        class="w-full h-full text-left"
                      >
                        <span>
                          <article
                            class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                            data-testid="product_card"
                            test-target="product-card"
                          >
                            <section
                              class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                            />
                            <div
                              class="flex-col"
                            >
                              <div
                                class="relative w-full"
                              >
                                <figure
                                  class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                                  test-target="product-card-AAA0000140729"
                                >
                                  <img
                                    alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück"
                                    src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                                  />
                                </figure>
                              </div>
                              <section
                                test-target="product-cards-variants"
                              >
                                <div
                                  class="min-h-[24px]"
                                  data-testid="empty-container"
                                />
                              </section>
                            </div>
                            <div
                              class="flex flex-col undefined"
                            >
                              <p
                                class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                                data-testid="product-name"
                                test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-14-Stück"
                              >
                                Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück
                              </p>
                              <div
                                class="mt-auto"
                                data-testid="product-price-section"
                              >
                                <div
                                  class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                                >
                                  <div
                                    class="min-h-5"
                                    data-testid="strike-price-container"
                                  />
                                  <div
                                    class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                                  >
                                    <p
                                      class="leading-[22px] self-end"
                                      data-testid="single-unit-main-price"
                                    >
                                      8,61 €
                                    </p>
                                  </div>
                                  <div
                                    class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                                    data-testid="units-count-container"
                                  />
                                </div>
                              </div>
                            </div>
                          </article>
                        </span>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <a
              class=""
              data-testid=""
              href="https://marketplace-test.makro.es/marketplace/search?q=hotel"
              test-target=""
            >
              <div
                class="flex justify-end mb-3"
              >
                <span>
                  <a
                    class="flex gap-2 px-4 py-2 text-[16px] h-[40px] font-lato items-center text-blue-interaction rounded-none font-extrabold bg-inherit text-blue-main !rounded-md !font-bold hover:bg-blue-shade-20"
                  >
                    CATALOG.DETAILS_PAGE.SERIES_CAROUSEL.SEE_ALL
                    <svg
                      aria-hidden="true"
                      class="ml-4px"
                      data-testid=""
                      fill="text-white-main"
                      height="100%"
                      viewBox="0 0 32 32"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <path
                        d="M4.001625,16.000375 C4.001625,15.447375 4.448625,15.000375 5.001625,15.000375 L24.587625,15.000375 L17.292625,7.705375 C16.901625,7.314375 16.901625,6.682375 17.292625,6.291375 C17.487625,6.096375 17.743625,5.998375 17.999625,5.998375 C18.255625,5.998375 18.511625,6.096375 18.706625,6.291375 L27.708625,15.292375 C27.799625,15.384375 27.873625,15.493375 27.923625,15.614375 C27.974625,15.735375 28.001625,15.866375 28.001625,16.000375 C28.001625,16.133375 27.974625,16.264375 27.923625,16.385375 C27.873625,16.506375 27.799625,16.615375 27.708625,16.707375 L18.706625,25.708375 C18.315625,26.099375 17.683625,26.099375 17.292625,25.708375 C16.901625,25.317375 16.901625,24.685375 17.292625,24.294375 L24.587625,17.000375 L5.001625,17.000375 C4.448625,17.000375 4.001625,16.552375 4.001625,16.000375 Z"
                        fill="currentColor"
                        stroke="none"
                      />
                    </svg>
                  </a>
                </span>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    data-testid="ai-carousel-1"
  >
    <div
      class="p-0 rounded-xs xs-lgMinus:relative xs-lgMinus:min-w-[100vw] xs-lgMinus:left-[-16px] mt-6 bg-white-main"
      data-testid="ai-advanced-carousel"
    >
      <div
        class="bg-white-main  pl-4 md:pl-0"
        data-testid="textbox-carousel-wrapper"
      >
        <div
          class="flex flex-col md:flex-row"
        >
          <div
            class="flex w-full md:w-[35%] lg:w-[30%] xl:w-[25%] py-4 md:py-6 md:pl-10 md:pr-6 md:gap-2 "
            data-testid="textbox-carousel-header-wrapper"
          >
            <header
              class="flex md:flex-col md:justify-center flex-col gap-2"
            >
              <div
                class="md:max-w-none flex flex-col gap-1 
          mb-0"
              >
                <div
                  class="flex gap-1 font-metro-ca-800 text-metro-blue-tint-20"
                >
                  <svg
                    aria-hidden="true"
                    class="block align-middle"
                    data-testid="svg-icon"
                    fill=""
                    height="100%"
                    viewBox="0 0 27 25"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                  >
                    <svg
                      fill="none"
                      height="100%"
                      viewBox="0 0 22 18"
                      width="100%"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        id="Group 2095"
                      >
                        <path
                          d="M7.82224 6.10618C7.70118 5.6673 7.30933 5.36719 6.86333 5.36719C6.41733 5.36719 6.0223 5.67053 5.90442 6.10618C5.0602 9.20414 4.56642 9.70433 1.50492 10.5595C1.07166 10.6821 0.775391 11.0791 0.775391 11.5308C0.775391 11.9826 1.07485 12.3828 1.50492 12.5022C4.56323 13.3573 5.05702 13.8575 5.90442 16.9587C6.02548 17.3976 6.41733 17.6977 6.86333 17.6977C7.30933 17.6977 7.70436 17.3944 7.82224 16.9587C8.66646 13.8608 9.16025 13.3606 12.2217 12.5022C12.655 12.3796 12.9513 11.9826 12.9513 11.5308C12.9513 11.0791 12.6518 10.6789 12.2217 10.5595C9.16343 9.70433 8.66964 9.20414 7.82224 6.10618ZM6.86014 13.764C6.33131 12.7442 5.66549 12.0698 4.6588 11.5341C5.66549 10.9984 6.33131 10.3239 6.86014 9.30418C7.38898 10.3239 8.05479 10.9984 9.06149 11.5341C8.05479 12.0698 7.38898 12.7442 6.86014 13.764Z"
                          fill="#F3CD0B"
                          id="Vector"
                        />
                        <path
                          d="M4.6582 11.5346C5.6649 12.0703 6.33071 12.7447 6.85955 13.7645C7.38838 12.7447 8.0542 12.0703 9.06089 11.5346C8.0542 10.9989 7.38838 10.3244 6.85955 9.30469C6.33071 10.3244 5.6649 10.9989 4.6582 11.5346Z"
                          fill="#FFED00"
                          id="Vector_2"
                        />
                        <path
                          d="M20.4946 5.017C18.0129 4.32319 17.6497 3.95531 16.9616 1.43821C16.8405 0.999334 16.4487 0.699219 16.0027 0.699219C15.5567 0.699219 15.1616 1.00256 15.0438 1.43821C14.3588 3.95208 13.9957 4.31996 11.5108 5.017C11.0775 5.13963 10.7812 5.53656 10.7812 5.98834C10.7812 6.44013 11.0807 6.84028 11.5108 6.95968C13.9925 7.6535 14.3556 8.02138 15.0438 10.5385C15.1648 10.9774 15.5567 11.2775 16.0027 11.2775C16.4487 11.2775 16.8437 10.9741 16.9616 10.5385C17.6465 8.02461 18.0097 7.65673 20.4946 6.95968C20.9278 6.83706 21.2241 6.44013 21.2241 5.98834C21.2241 5.53656 20.9246 5.1364 20.4946 5.017ZM16.0027 7.47924C15.6268 6.84028 15.1648 6.36913 14.5309 5.98834C15.1616 5.60755 15.6268 5.13963 16.0027 4.49745C16.3786 5.1364 16.8405 5.60755 17.4745 5.98834C16.8437 6.36913 16.3786 6.83706 16.0027 7.47924Z"
                          fill="#F3CD0B"
                          id="Vector_3"
                        />
                        <path
                          d="M14.5293 5.98699C15.1633 6.36778 15.6252 6.83893 16.0011 7.47788C16.377 6.8357 16.8421 6.36778 17.4729 5.98699C16.839 5.6062 16.377 5.13505 16.0011 4.49609C15.6252 5.13828 15.1601 5.6062 14.5293 5.98699Z"
                          fill="#FFED00"
                          id="Vector_4"
                        />
                      </g>
                    </svg>
                  </svg>
                  <p>
                    CATALOG.CATALOG_PAGE.CATALOG_TOOLBAR.AI_EXPLORER
                  </p>
                  <svg
                    aria-hidden="true"
                    class="mt-1"
                    data-testid=""
                    fill=""
                    height="20px"
                    viewBox="0 0 27 25"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                  >
                    <g
                      fill="#33578E"
                      id="icon-/-alert-info"
                      stroke="none"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M0 9.99939C0 4.48566 4.48539 0 10 0C15.5146 0 20 4.48566 20 9.99939C20 15.5143 15.5146 20 10 20C4.48539 20 0 15.5143 0 9.99939ZM1.8184 9.99939C1.8184 14.5117 5.48794 18.1815 10 18.1815C14.5108 18.1815 18.1816 14.5117 18.1816 9.99939C18.1816 5.48827 14.5108 1.81851 10 1.81851C5.48794 1.81851 1.8184 5.48827 1.8184 9.99939ZM8.675 5.69766C8.675 4.96783 9.2678 4.375 9.99758 4.375C10.7262 4.375 11.3202 4.96783 11.3202 5.69766C11.3202 6.42749 10.7262 7.02033 9.99758 7.02033C9.2678 7.02033 8.675 6.42749 8.675 5.69766ZM10.8865 15.7859V15.0231V14.3996V13.9674V8.98099C10.8865 8.48295 10.4854 8.07802 9.98888 8.0718C9.99366 8.07176 9.99846 8.07173 10.0033 8.07173H9.97728C9.98115 8.07173 9.98502 8.07176 9.98888 8.0718C9.3735 8.07802 9.06738 8.48295 9.06738 8.98099C9.06738 9.23008 9.06699 9.27667 9.06659 9.32361C9.06619 9.37125 9.06578 9.41925 9.06578 9.67971L9.06808 9.89024V13.9674V14.3217V14.8553V15.7859H10.8865Z"
                        fill="currentColor"
                        fill-rule="evenodd"
                        id="alert-info"
                      />
                    </g>
                  </svg>
                </div>
                <h1
                  class="font-extrabold text-metro-blue-main font-metro-ca uppercase text-[20px] md:text-2xl"
                  data-testid="title"
                >
                  hotel description
                </h1>
              </div>
            </header>
          </div>
          <div
            class="block relative md:w-[65%] lg:w-[70%] xl:w-[75%] md:pt-4"
          >
            <div
              class="w-full 
    block
    relative
    pb-4
    md:pb-0
  "
            >
              <div
                data-testid="mobile-container"
              >
                <div
                  class="flex gap-3 overflow-auto max-w-[100vw] mx-auto pr-4 undefined"
                  data-testid="mobileScroll"
                >
                  <div
                    data-testid="mobile-swiper-slider"
                    test-target="product-carousel-item-mobile"
                  >
                    <a
                      class=""
                      data-testid=""
                      href="https://marketplace-test.makro.es/marketplace/product/f498e981-54a1-4b6c-9260-a6922e759abc"
                      test-target=""
                    >
                      <div
                        class="w-full h-full text-left"
                      >
                        <span>
                          <article
                            class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                            data-testid="product_card"
                            test-target="product-card"
                          >
                            <section
                              class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                            />
                            <div
                              class="flex-col"
                            >
                              <div
                                class="relative w-full"
                              >
                                <figure
                                  class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                                  test-target="product-card-AAA0000140729"
                                >
                                  <img
                                    alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück"
                                    src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                                  />
                                </figure>
                              </div>
                              <section
                                test-target="product-cards-variants"
                              >
                                <div
                                  class="min-h-[24px]"
                                  data-testid="empty-container"
                                />
                              </section>
                            </div>
                            <div
                              class="flex flex-col justify-between flex-grow w-full"
                            >
                              <p
                                class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                                data-testid="product-name"
                                test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-12-Stück"
                              >
                                Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 12 Stück
                              </p>
                              <div
                                class="mt-auto"
                                data-testid="product-price-section"
                              >
                                <div
                                  class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                                >
                                  <div
                                    class="min-h-5"
                                    data-testid="strike-price-container"
                                  />
                                  <div
                                    class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                                  >
                                    <p
                                      class="leading-[22px] self-end"
                                      data-testid="single-unit-main-price"
                                    >
                                      8,61 €
                                    </p>
                                  </div>
                                  <div
                                    class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                                    data-testid="units-count-container"
                                  />
                                </div>
                              </div>
                              <div>
                                <span>
                                  <div
                                    class="flex items-center relative pt-2"
                                  >
                                    <div
                                      class="mr-1.5 text-[14px] font-lato leading-5 text-[#677283] font-normal"
                                    >
                                      SHARED.SPONSORED.LABEL.TEXT
                                    </div>
                                    <div
                                      class="w-24px h-24px bg-blue-tint-80 rounded-full flex items-center relative justify-center cursor-pointer before:content-['i'] before:text-blue-shade-60 before:text-sm !w-4 !h-4 border-[1px] !border-[#677283] !bg-transparent before:!text-[#677283] font-normal"
                                    />
                                  </div>
                                </span>
                              </div>
                            </div>
                          </article>
                        </span>
                      </div>
                    </a>
                  </div>
                  <div
                    data-testid="mobile-swiper-slider"
                    test-target="product-carousel-item-mobile"
                  >
                    <a
                      class=""
                      data-testid=""
                      href="https://marketplace-test.makro.es/marketplace/product/f498e981-54a1-4b6c-9260-a6922e759def"
                      test-target=""
                    >
                      <div
                        class="w-full h-full text-left"
                      >
                        <span>
                          <article
                            class="not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px] flex-col w-[160px] md:max-w-[230px]"
                            data-testid="product_card"
                            test-target="product-card"
                          >
                            <section
                              class="absolute top-0 h-[24px] flex gap-[6px] z-[5]"
                            />
                            <div
                              class="flex-col"
                            >
                              <div
                                class="relative w-full"
                              >
                                <figure
                                  class="w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]"
                                  test-target="product-card-AAA0000140729"
                                >
                                  <img
                                    alt="Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück"
                                    src="https://pp-de-metro-markets.imgix.net/item_image/633c4f9b-d008-4d6e-a87b-99a1d47a9c3c?h=148&ixlib=php-2.3.0&q=100&w=148"
                                  />
                                </figure>
                              </div>
                              <section
                                test-target="product-cards-variants"
                              >
                                <div
                                  class="min-h-[24px]"
                                  data-testid="empty-container"
                                />
                              </section>
                            </div>
                            <div
                              class="flex flex-col undefined"
                            >
                              <p
                                class="text-[14px] font-normal text-primary-main leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'} xs:line-clamp-2 min-h-11 lg:line-clamp-2"
                                data-testid="product-name"
                                test-target="product-card-Bauscher-Teller-flach-31cm-8050/31-Dialog-Weiss,-14-Stück"
                              >
                                Bauscher Teller flach 31cm 8050/31 Dialog Weiss, 14 Stück
                              </p>
                              <div
                                class="mt-auto"
                                data-testid="product-price-section"
                              >
                                <div
                                  class=" prose flex flex-col text-[14px] font-light leading-5 text-metro-blue-main antialiased"
                                >
                                  <div
                                    class="min-h-5"
                                    data-testid="strike-price-container"
                                  />
                                  <div
                                    class="flex flex-row flex-wrap md:flex-no-wrap box-border items-end gap-x-1.5 gap-y-1 min-h-6 mr-[-11px] leading-6 font-extrabold text-[18px] text-primary-main"
                                  >
                                    <p
                                      class="leading-[22px] self-end"
                                      data-testid="single-unit-main-price"
                                    >
                                      8,61 €
                                    </p>
                                  </div>
                                  <div
                                    class="flex gap-1 text-[14px] font-normal text-[#677283] h-0"
                                    data-testid="units-count-container"
                                  />
                                </div>
                              </div>
                            </div>
                          </article>
                        </span>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <a
              class=""
              data-testid=""
              href="https://marketplace-test.makro.es/marketplace/search?q=teller"
              test-target=""
            >
              <div
                class="flex justify-end mb-3"
              >
                <span>
                  <a
                    class="flex gap-2 px-4 py-2 text-[16px] h-[40px] font-lato items-center text-blue-interaction rounded-none font-extrabold bg-inherit text-blue-main !rounded-md !font-bold hover:bg-blue-shade-20"
                  >
                    CATALOG.DETAILS_PAGE.SERIES_CAROUSEL.SEE_ALL
                    <svg
                      aria-hidden="true"
                      class="ml-4px"
                      data-testid=""
                      fill="text-white-main"
                      height="100%"
                      viewBox="0 0 32 32"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                      <path
                        d="M4.001625,16.000375 C4.001625,15.447375 4.448625,15.000375 5.001625,15.000375 L24.587625,15.000375 L17.292625,7.705375 C16.901625,7.314375 16.901625,6.682375 17.292625,6.291375 C17.487625,6.096375 17.743625,5.998375 17.999625,5.998375 C18.255625,5.998375 18.511625,6.096375 18.706625,6.291375 L27.708625,15.292375 C27.799625,15.384375 27.873625,15.493375 27.923625,15.614375 C27.974625,15.735375 28.001625,15.866375 28.001625,16.000375 C28.001625,16.133375 27.974625,16.264375 27.923625,16.385375 C27.873625,16.506375 27.799625,16.615375 27.708625,16.707375 L18.706625,25.708375 C18.315625,26.099375 17.683625,26.099375 17.292625,25.708375 C16.901625,25.317375 16.901625,24.685375 17.292625,24.294375 L24.587625,17.000375 L5.001625,17.000375 C4.448625,17.000375 4.001625,16.552375 4.001625,16.000375 Z"
                        fill="currentColor"
                        stroke="none"
                      />
                    </svg>
                  </a>
                </span>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
