import {
  fireEvent,
  render,
  screen,
  waitFor,
  within
} from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'

import { config } from '@core/config'
import { http } from '@core/services/http/http-request'

import { useContactSeller } from '@modules/message-center/components/ContactSeller/useContactSeller'

import { order, orderLine } from '../../__tests__/fixtures'
import { OrderDetailOrderLineContactSeller } from '../OrderDetailOrderLineContactSeller'

// Mock functions that need to be available for jest.mock calls
const gtagSellerEventMock = jest.fn()
const gtagMetroEventMock = jest.fn()

Object.defineProperty(window, 'location', {
  value: {
    href: ''
  },
  writable: true
})
jest.mock('@core/services/http/http-request', () => ({
  http: jest.fn()
}))
jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    buyerGateway: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    svcBuyerAccountBaseUrl: 'http://mock-buyer-url'
  }
}))
jest.mock('@core/redux/features/market/useLocale', () => ({
  useLocale: () => 'de-DE'
}))
jest.mock('@core/hooks', () => ({
  useMarket: jest.fn(() => 'de')
}))
jest.mock('@core/redux/features/user/useUser', () => ({
  useUser: () => ({
    isAuthorized: true,
    fetchAccountInfo: jest.fn()
  })
}))
jest.mock(
  '@modules/account/components/orders-history/hooks/useActiveContext',
  () => ({
    useActiveContext: () => 'test-context'
  })
)
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
  useAppSelect: jest.fn(),
  useSelector: jest.fn()
}))

// Mock the feature flag hook
jest.mock('@core/redux/features/featureFlags', () => ({
  useFeatureFlag: jest.fn(() => false) // Default to false
}))

// Mock the useContactSeller hook
jest.mock(
  '@modules/message-center/components/ContactSeller/useContactSeller',
  () => ({
    useContactSeller: jest.fn(() => ({
      isMetroSeller: false,
      handleContactSeller: jest.fn(),
      isCheckingEligibility: false,
      isMessageCenterC2AFeatureEnabled: false
    }))
  })
)

// Mock the translation hook
jest.mock('@core/hooks/useTranslation', () => ({
  useAccountTranslation: () => ({
    t: (key: string, params?: any) => {
      if (params) {
        return key // Return the key as-is for testing
      }
      return key
    }
  })
}))
jest.mock(
  '@modules/account/components/orders-history/hooks/useOrdersHistoryGAEvents',
  () => ({
    useOrdersHistoryGAEvents: () => ({
      trackContactSupport: gtagMetroEventMock,
      trackContactSeller: gtagSellerEventMock
    })
  })
)
describe('OrderDetailOrderLineContactSeller', () => {
  describe('Seller is not METRO', () => {
    it('should show the dropdown button', () => {
      render(
        <OrderDetailOrderLineContactSeller
          orderId="1234"
          orderNumber={order.orderNumber}
          orderLine={orderLine}
        />
      )

      expect(
        screen.getByTestId('order-detail-order-line-contact-seller')
      ).toBeInTheDocument()
    })

    describe('desktop version', () => {
      it('dropdown should have 2 options when the dropdown is displayed', () => {
        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={orderLine}
          />
        )

        fireEvent.click(
          screen.getByTestId('order-detail-order-line-contact-seller')
        )
        expect(
          // eslint-disable-next-line testing-library/no-node-access
          screen.getByTestId('dropdown-options').querySelectorAll('div')
        ).toHaveLength(2)
      })

      it('href should be mailto with the email, subject and body set', async () => {
        const expectedMailtoLink = `mailto:${orderLine.sellerDetails.email}?subject=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT&body=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_BODY`

        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={orderLine}
          />
        )

        fireEvent.click(
          screen.getByTestId('order-detail-order-line-contact-seller')
        )

        fireEvent.click(
          // eslint-disable-next-line testing-library/no-node-access
          screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
        )

        await waitFor(() => {
          expect(window.location.href).toBe(expectedMailtoLink)
        })
      })

      it('href should be mailto with the email, subject and body set', async () => {
        const expectedMailtoLink = `mailto:${orderLine.sellerDetails.email}?subject=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_SUBJECT&body=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_BODY`

        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={orderLine}
          />
        )

        fireEvent.click(
          screen.getByTestId('order-detail-order-line-contact-seller')
        )

        fireEvent.click(
          // eslint-disable-next-line testing-library/no-node-access
          screen.getByTestId('dropdown-options').querySelectorAll('div')[1]
        )

        await waitFor(() => {
          expect(window.location.href).toBe(expectedMailtoLink)
        })
      })
      describe('GA', () => {
        it('should call to track function when is clicked', () => {
          render(
            <OrderDetailOrderLineContactSeller
              orderId="1234"
              orderNumber={order.orderNumber}
              orderLine={{
                ...orderLine
              }}
            />
          )

          fireEvent.click(
            screen.getByTestId('order-detail-order-line-contact-seller')
          )

          fireEvent.click(
            // eslint-disable-next-line testing-library/no-node-access
            screen.getByTestId('dropdown-options').querySelectorAll('div')[0]
          )

          expect(gtagSellerEventMock).toBeCalled()
        })
      })
    })

    describe('mobile version', () => {
      it('should open a sidebar when the button is clicked', () => {
        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={orderLine}
          />
        )

        fireEvent.click(
          screen.getByTestId(
            'order-detail-order-line-contact-seller-mobile-button'
          )
        )
        expect(screen.getByTestId('side-bar-close')).toBeInTheDocument()
      })

      it('href should be mailto with the email, subject and body set for requesting delivery time', async () => {
        const expectedMailtoLink = `mailto:${orderLine.sellerDetails.email}?subject=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT&body=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_BODY`
        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={orderLine}
          />
        )

        fireEvent.click(
          screen.getByTestId(
            'order-detail-order-line-contact-seller-mobile-button'
          )
        )
        await userEvent.click(
          within(screen.getByTestId('side-bar')).getAllByRole('button')[0]
        )

        expect(window.location.href).toBe(expectedMailtoLink)
      })

      it('href should be mailto with the email, subject and body set for requesting enquiry', async () => {
        const expectedMailtoLink = `mailto:${orderLine.sellerDetails.email}?subject=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_SUBJECT&body=PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_BODY`
        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={orderLine}
          />
        )

        fireEvent.click(
          screen.getByTestId(
            'order-detail-order-line-contact-seller-mobile-button'
          )
        )
        await userEvent.click(
          within(screen.getByTestId('side-bar')).getAllByRole('button')[2]
        )

        expect(window.location.href).toBe(expectedMailtoLink)
      })
    })
  })

  describe('Seller is METRO', () => {
    beforeEach(() => {
      // Mock for Metro seller
      ;(useContactSeller as jest.Mock).mockReturnValue({
        isMetroSeller: true,
        handleContactSeller: jest.fn(),
        isCheckingEligibility: false,
        isMessageCenterC2AFeatureEnabled: false
      })

      // Enable feature flag for Metro seller tests
      const { useFeatureFlag } = require('@core/redux/features/featureFlags')
      ;(useFeatureFlag as jest.Mock).mockReturnValue(true)
    })

    afterEach(() => {
      // Reset mocks after each test
      const { useFeatureFlag } = require('@core/redux/features/featureFlags')
      ;(useFeatureFlag as jest.Mock).mockReturnValue(false)
    })

    it('should show the button', () => {
      render(
        <OrderDetailOrderLineContactSeller
          orderId="1234"
          orderNumber={order.orderNumber}
          orderLine={{
            ...orderLine,
            sellerDetails: {
              ...orderLine.sellerDetails,
              id: config.METRO_ID
            }
          }}
        />
      )

      expect(
        screen.getByTestId('order-detail-order-line-contact-seller-metro')
      ).toBeInTheDocument()
    })

    it('should do a get request to login/sso', async () => {
      const closeSpy = jest.fn()
      window.open = jest.fn()
      ;(http as jest.Mock).mockResolvedValue({ data: 'page.com' })

      window.open = jest.fn().mockReturnValue({ close: closeSpy })

      render(
        <OrderDetailOrderLineContactSeller
          orderId="1234"
          orderNumber={order.orderNumber}
          orderLine={{
            ...orderLine,
            sellerDetails: {
              ...orderLine.sellerDetails,
              id: config.METRO_ID
            }
          }}
        />
      )

      fireEvent.click(
        screen.getByTestId('order-detail-order-line-contact-seller-metro')
      )

      // Wait for the async call to complete
      await waitFor(() => {
        expect(http).toHaveBeenCalledWith(
          'marktplatz/app-api/account/order-history/get-login-to-sso-url',
          {
            method: 'POST',
            data: {
              activeContext: 'test-context'
            }
          },
          {
            market: 'de',
            locale: 'de-DE',
            withSessionId: true
          }
        )
      })
      await waitFor(() => {
        expect(window.open).toBeCalledWith('page.com', '_blank')
      })
      expect(window.open).toBeCalledTimes(1)
    })
    describe('GA', () => {
      it('should call to track function when is clicked', () => {
        render(
          <OrderDetailOrderLineContactSeller
            orderId="1234"
            orderNumber={order.orderNumber}
            orderLine={{
              ...orderLine,
              sellerDetails: {
                ...orderLine.sellerDetails,
                id: config.METRO_ID
              }
            }}
          />
        )

        fireEvent.click(
          screen.getByTestId('order-detail-order-line-contact-seller-metro')
        )

        expect(gtagMetroEventMock).toBeCalled()
      })
    })
  })
})
