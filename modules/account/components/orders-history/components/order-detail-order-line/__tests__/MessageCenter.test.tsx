import { fireEvent, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/router'

import { renderWithProviders } from '@core/utils/testing'

// eslint-disable-next-line no-restricted-imports
import { useMessageCenterEligibility } from '../../../hooks/useMessageCenterEligibility'
// eslint-disable-next-line no-restricted-imports
import { useOrdersHistoryGAEvents } from '../../../hooks/useOrdersHistoryGAEvents'
import { OrderDetailOrderLineContactSeller } from '../OrderDetailOrderLineContactSeller'

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    buyerGateway: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    svcBuyerAccountBaseUrl: 'http://mock-buyer-url'
  }
}))

jest.mock('@core/services/http/http-request', () => ({
  http: jest.fn(() => Promise.resolve({ data: 'mock-sso-url' }))
}))
jest.mock('../../../hooks/useOrdersHistoryGAEvents')
jest.mock('../../../hooks/useMessageCenterEligibility')

jest.mock('@core/config', () => ({
  config: {
    METRO_ID: 'b4b309e0-9f53-4c9b-b639-3913b7131996'
  },
  getAppBuyerUrl: jest.fn(() => 'http://mock-buyer-url')
}))

jest.mock('@core/hooks', () => ({
  useMarket: jest.fn(() => 'de')
}))

jest.mock('@core/redux/features/market/useLocale', () => ({
  useLocale: () => 'de-DE'
}))

jest.mock('@core/hooks/useTranslation', () => ({
  useAccountTranslation: () => ({
    t: (key: string, params?: any) => key
  })
}))

jest.mock('../../../hooks/useActiveContext', () => ({
  useActiveContext: () => 'test-context'
}))

jest.mock('@modules/message-center/components/ContactSeller/useContactSeller')

const mockUseContactSeller =
  require('@modules/message-center/components/ContactSeller/useContactSeller').useContactSeller

describe('OrderDetailOrderLineContactSeller', () => {
  const mockProps = {
    orderId: 'order123',
    orderNumber: 'ON123',
    orderLine: {
      sellerDetails: {
        id: 'non-metro-seller-id',
        email: '<EMAIL>'
      }
    }
  }

  const mockPush = jest.fn()
  const mockTrackContactSupport = jest.fn()
  const mockUseMessageCenterEligibility =
    useMessageCenterEligibility as jest.MockedFunction<
      typeof useMessageCenterEligibility
    >
  const mockUseOrdersHistoryGAEvents =
    useOrdersHistoryGAEvents as jest.MockedFunction<
      typeof useOrdersHistoryGAEvents
    >
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock window.location.href to prevent JSDOM navigation error
    Object.defineProperty(window, 'location', {
      value: {
        href: ''
      },
      writable: true
    })

    // Mock window.open to track navigation
    window.open = jest.fn()

    mockUseRouter.mockReturnValue({
      push: mockPush,
      query: { context: 'personal' }
    } as any)

    mockUseOrdersHistoryGAEvents.mockReturnValue({
      trackContactSupport: mockTrackContactSupport,
      trackContactSeller: jest.fn()
    } as any)
  })

  it('should redirect to message center when canRedirectToMessageCenter is true', async () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: true,
      isCheckingEligibility: false,
      error: null,
      canRedirectToMessageCenter: true,
      isMessageCenterC2AFeatureEnabled: true
    })
    ;(mockUseContactSeller as jest.Mock).mockReturnValue({
      isMetroSeller: true,
      handleContactSeller: () => {
        window.location.href = '/message-center/order123/non-metro-seller-id'
      },
      isCheckingEligibility: false,
      isMessageCenterC2AFeatureEnabled: true
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(
      screen.getByTestId('order-detail-order-line-contact-seller-metro')
    )

    await waitFor(() => {
      expect(window.location.href).toContain(
        '/message-center/order123/non-metro-seller-id'
      )
    })
  })

  it('should not redirect to message center when canRedirectToMessageCenter is false', async () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: false,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: false
    })
    ;(mockUseContactSeller as jest.Mock).mockReturnValue({
      isMetroSeller: false,
      handleContactSeller: jest.fn(),
      isCheckingEligibility: false,
      isMessageCenterC2AFeatureEnabled: false
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(
      screen.getByTestId('order-detail-order-line-contact-seller')
    )

    await waitFor(() => {
      expect(window.location.href).not.toContain('/message-center')
    })
  })

  it('should not redirect to message center when feature flag is disabled', async () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: false,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: false
    })
    ;(mockUseContactSeller as jest.Mock).mockReturnValue({
      isMetroSeller: false,
      handleContactSeller: jest.fn(),
      isCheckingEligibility: false,
      isMessageCenterC2AFeatureEnabled: false
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(
      screen.getByTestId('order-detail-order-line-contact-seller')
    )

    // Simulate selecting the first dropdown option (if needed, you may need to mock Dropdown's onSelectOption)
    // For now, just check that mockPush is not called (as in the original test)
    await waitFor(() => {
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  it('should not redirect to message center when there is an error', async () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: false,
      error: new Error('Network error'),
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: false
    })
    ;(mockUseContactSeller as jest.Mock).mockReturnValue({
      isMetroSeller: false,
      handleContactSeller: jest.fn(),
      isCheckingEligibility: false,
      isMessageCenterC2AFeatureEnabled: false
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    fireEvent.click(
      screen.getByTestId('order-detail-order-line-contact-seller')
    )

    await waitFor(() => {
      expect(window.location.href).not.toContain('/message-center')
    })
  })

  it('should disable metro seller button when checking eligibility', () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: null,
      isCheckingEligibility: true,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })
    ;(mockUseContactSeller as jest.Mock).mockReturnValue({
      isMetroSeller: true,
      handleContactSeller: jest.fn(),
      isCheckingEligibility: true,
      isMessageCenterC2AFeatureEnabled: true
    })

    const metroProps = {
      ...mockProps,
      orderLine: {
        ...mockProps.orderLine,
        sellerDetails: {
          ...mockProps.orderLine.sellerDetails,
          id: 'b4b309e0-9f53-4c9b-b639-3913b7131996' // Use actual METRO_ID from config
        }
      }
    }

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(metroProps as any)} />
    )

    const metroButton = screen.getByTestId(
      'order-detail-order-line-contact-seller-metro'
    )
    expect(metroButton).toBeDisabled()
  })

  it('should disable button when isCheckingEligibility is true', () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: true,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })
    ;(mockUseContactSeller as jest.Mock).mockReturnValue({
      isMetroSeller: true,
      handleContactSeller: jest.fn(),
      isCheckingEligibility: true,
      isMessageCenterC2AFeatureEnabled: true
    })

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(mockProps as any)} />
    )

    // When message center FF is enabled, we show a simple button
    expect(
      screen.getByTestId('order-detail-order-line-contact-seller-metro')
    ).toBeDisabled()
  })

  it('should disable METRO button when isCheckingEligibility is true', () => {
    mockUseMessageCenterEligibility.mockReturnValue({
      isEligible: false,
      isCheckingEligibility: true,
      error: null,
      canRedirectToMessageCenter: false,
      isMessageCenterC2AFeatureEnabled: true
    })

    const metroProps = {
      ...mockProps,
      orderLine: {
        ...mockProps.orderLine,
        sellerDetails: {
          ...mockProps.orderLine.sellerDetails,
          id: 'b4b309e0-9f53-4c9b-b639-3913b7131996' // Use actual METRO_ID from config
        }
      }
    }

    renderWithProviders(
      <OrderDetailOrderLineContactSeller {...(metroProps as any)} />
    )
    expect(
      screen.getByTestId('order-detail-order-line-contact-seller-metro')
    ).toBeDisabled()
  })
})
