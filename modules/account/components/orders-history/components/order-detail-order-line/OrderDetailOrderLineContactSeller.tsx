import { config } from '@core/config'
import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { ContactSellerButton } from '@modules/message-center/components/ContactSeller/ContactSellerButton'
import { useContactSeller } from '@modules/message-center/components/ContactSeller/useContactSeller'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'
import { Dropdown } from '@modules/shared/components/Dropdown'
import { Option } from '@modules/shared/types'

import { CONTACT_SELLER_OPTIONS } from '../../constants'
import { OrderLine } from '../../types'
import { OrderDetailOrderLineContactSellerMobile } from './OrderDetailOrderLineContactSellerMobile'

interface Props {
  orderId: string
  orderNumber: string
  orderLine: OrderLine
}

export const OrderDetailOrderLineContactSeller = ({
  orderId,
  orderNumber,
  orderLine
}: Props) => {
  const isMessageCenterC2AFeatureEnabled = useFeatureFlag(
    FeatureFlag.FF_CCS_MESSAGE_CENTER_ORDER_C2A
  )
  const { t } = useAccountTranslation()
  const { isMetroSeller } = useContactSeller(orderId, orderNumber, orderLine)

  const handleSellerButtonClick = (option: Option) => {
    if (option.id === 1) {
      handleSendEmail(
        orderLine.sellerDetails.email,
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT',
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_BODY'
      )
    } else if (option.id === 2) {
      handleSendEmail(
        orderLine.sellerDetails.email,
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_SUBJECT',
        'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_BODY'
      )
    }
  }

  const handleSendEmail = (email: string, subject: string, body: string) => {
    const mailtoLink = `mailto:${email}?subject=${t(subject, {
      orderNumber: orderNumber
    })}&body=${t(body, {
      orderNumber: orderNumber
    })}`
    window.location.href = mailtoLink
  }

  return (
    <>
      {isMessageCenterC2AFeatureEnabled || isMetroSeller ? (
        <ContactSellerButton
          orderId={orderId}
          orderNumber={orderNumber}
          orderLine={orderLine}
          dataTestId="order-detail-order-line-contact-seller-metro"
        />
      ) : (
        <>
          <Dropdown
            ariaLabel={t(
              'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER'
            )}
            buttonVariant={BUTTON_VARIANTS.infoAccessible}
            dataTestId="order-detail-order-line-contact-seller"
            prefixSelected={t(
              'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER'
            )}
            className={{
              button: 'font-semibold font-lato min-h-[40px] leading-[21px]',
              option: 'h-[40px] leading-[24px] text-regular text-blue-main',
              content: 'w-max right-0 min-w-full',
              dropdown: 'hidden lg:block'
            }}
            options={CONTACT_SELLER_OPTIONS.map((option) => ({
              id: option.id,
              value: t(option.value)
            }))}
            onSelectOption={handleSellerButtonClick}
          />
          <div className="lg:hidden">
            <OrderDetailOrderLineContactSellerMobile
              onClickButton={handleSellerButtonClick}
            />
          </div>
        </>
      )}
    </>
  )
}
