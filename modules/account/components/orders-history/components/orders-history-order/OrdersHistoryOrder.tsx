import { useIsMobile } from '@core/hooks/useIsMobile'

import { useIsSellerConversation } from '@modules/message-center/hooks/useIsSellerConversation'
import { Divider } from '@modules/shared/components'

import { OrderHistoryItem } from '../../types'
import { InstallationServicesOrderHistory } from '../installation-services/InstallationServicesOrderHistory'
import { OrdersHistoryOrderHeader } from '../orders-history-order-header/OrdersHistoryOrderHeader'
import { OrdersHistoryOrderLine } from '../orders-history-order-line/OrdersHistoryOrderLine'

interface Props {
  order: OrderHistoryItem
  autoFocus: boolean
}

export const OrdersHistoryOrder = ({ order, autoFocus }: Props) => {
  const isMobile = useIsMobile()
  const isOpenSellerConversation = useIsSellerConversation(order.orderId) // should be used here to fetch chats just once

  return (
    <div
      className="bg-[#F2F5FC] lg:bg-white-main pt-4 pl-4 pr-4 lg:p-0px"
      data-testid="orders-history-order"
    >
      <div className="bg-white-main lg:px-16px lg:pt-16px border-[1px] border-[#E5E7EA] rounded-[2px] [box-shadow:0px_1px_4px_rgba(0,20,50,.2)] shadow-[0_1px_4px_rgba(0,20,50,0.2)] lg:border-none lg:rounded-none lg:[box-shadow:none] lg:shadow-none">
        <OrdersHistoryOrderHeader order={order} autoFocus={autoFocus} />
        <div className="m-4 lg:m-0">
          {order.orderLines.map((orderLine, index) => {
            return (
              <div key={orderLine.orderLineId}>
                {!orderLine.referenceLineId ? (
                  <OrdersHistoryOrderLine
                    showContactSellerButton={isOpenSellerConversation(
                      orderLine.sellerDetails.id
                    )}
                    orderLine={orderLine}
                    orderNumber={order.orderNumber}
                    orderId={order.orderId}
                  />
                ) : (
                  <InstallationServicesOrderHistory orderLine={orderLine} />
                )}
                {isMobile && index < order.orderLines.length - 1 && <Divider />}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
