import { screen, waitFor } from '@testing-library/react'

import { http } from '@core/services/http/http-request'
import { renderWithProviders } from '@core/utils/testing'

import { order } from '../../__tests__/fixtures'
import { OrdersHistoryContainer } from '../OrdersHistoryContainer'

jest.mock('@core/services/http/http-request')
jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    buyerGateway: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))
jest.mock('@core/redux/features/market/useLocale', () => ({
  useLocale: () => 'de-DE'
}))
jest.mock('@core/hooks', () => ({
  useMarket: jest.fn(() => 'de')
}))
jest.mock('@core/redux', () => ({
  useUser: jest.fn(() => ({
    isAuthorized: true,
    fetchAccountInfo: jest.fn()
  })),
  useAppSelect: jest.fn(),
  useAppDispatch: jest.fn(() => jest.fn())
}))
jest.mock(
  '@modules/account/components/orders-history/hooks/useActiveContext',
  () => ({
    useActiveContext: () => 'test-context'
  })
)
jest.mock(
  '@modules/account/components/orders-history/hooks/useOrdersHistoryGAEvents',
  () => ({
    useOrdersHistoryGAEvents: () => ({
      trackViewMoreOrders: jest.fn(),
      trackViewOrderDetails: jest.fn()
    })
  })
)
jest.mock('@modules/message-center/hooks/useIsSellerConversation', () => ({
  useIsSellerConversation: () => jest.fn(() => true)
}))

describe('OrdersHistoryContainer', () => {
  it('should load the data from the BE', () => {
    renderWithProviders(<OrdersHistoryContainer />)

    expect(http).toHaveBeenCalledWith(
      'marktplatz/app-api/account/order-history/get-orders',
      {
        method: 'POST',
        data: {
          activeContext: 'test-context',
          pageState: {
            offset: 0,
            limit: 3
          }
        }
      },
      {
        market: 'de',
        locale: 'de-DE',
        withSessionId: true
      }
    )
  })

  it('should show the 1 orders from the response', async () => {
    ;(http as jest.Mock).mockResolvedValue({
      data: {
        totalCount: 1,
        items: [order]
      }
    })
    renderWithProviders(<OrdersHistoryContainer />)
    await waitFor(() => {
      expect(screen.getAllByTestId('orders-history-order')).toHaveLength(1)
    })
  })

  it('should show the no order page when there are no orders', async () => {
    ;(http as jest.Mock).mockResolvedValue({
      data: {
        totalCount: 0,
        items: []
      }
    })
    renderWithProviders(<OrdersHistoryContainer />)
    await waitFor(() => {
      expect(screen.getByTestId('orders-history-no-orders')).toBeVisible()
    })
  })

  it('should show the error page when the backend returns initially an error', async () => {
    ;(http as jest.Mock).mockRejectedValue(
      new Error('Internal how to wait to Server Error')
    )
    renderWithProviders(<OrdersHistoryContainer />)
    await waitFor(() => {})
    await waitFor(() => {
      expect(screen.getByTestId('orders-history-error')).toBeVisible()
    })
  })

  it('should show first the loader and then the orders ', async () => {
    ;(http as jest.Mock).mockResolvedValue({
      data: {
        totalCount: 1,
        items: [order]
      }
    })
    renderWithProviders(<OrdersHistoryContainer />)
    expect(screen.getByTestId('orders-loading-container-loading')).toBeVisible()
    await waitFor(() => {
      expect(
        screen.queryByTestId('orders-loading-container-loading')
      ).not.toBeInTheDocument()
    })
    await waitFor(() => {
      expect(screen.getByTestId('orders-history-order')).toBeInTheDocument()
    })
  })
})
