import { cn } from '@/lib/utils'
import { useRef, useState } from 'react'

import { config } from '@core/config'
import { useMarket } from '@core/hooks'
import { useIsMobile } from '@core/hooks/useIsMobile'
import { useAccountTranslation } from '@core/hooks/useTranslation'

import { handleEnterOrSpacePress } from '@shared/utils/accessibility.utils'

import {
  extraInformation,
  extraInformationNotBold,
  labelClassNames,
  overridedLabelText
} from '../../constants'
import { OrderLine } from '../../types'
import { OrderLineStatusView } from '../../types/order-line-status-view'
import {
  convertOrderLineStatusAndTrackingStatusToOrderLineStatusView,
  formatDate,
  formatDateRange
} from '../../utils/utils'
import { InfoModal } from '../info-modal/InfoModal'
import { ReturnInstructionsSidebar } from '../return-instructions/ReturnInstructionsSidebar'

interface Props {
  orderLine: OrderLine
  trackingIndex?: number
}

export const OrderLineStatusInfo = ({ orderLine, trackingIndex }: Props) => {
  const { t } = useAccountTranslation()

  const market = useMarket()
  const isMobile = useIsMobile()
  const [showReasonsModal, setShowReasonsModal] = useState(false)
  const [showReturnInstructionsSidebar, setShowReturnInstructionsSidebar] =
    useState(false)

  const showViewRejectedReasonsButtonRef = useRef<HTMLButtonElement>(null)
  const showViewCancelReasonsButtonRef = useRef<HTMLButtonElement>(null)

  const orderLineStatusView =
    convertOrderLineStatusAndTrackingStatusToOrderLineStatusView(
      orderLine,
      trackingIndex
    )

  const labelClassName = labelClassNames[orderLineStatusView]
  let extraInformationText = extraInformation[orderLineStatusView]
  let label = overridedLabelText[orderLineStatusView] ?? orderLine.status?.title
  let extraInformationDate: string | undefined

  const isShowingCancellationViewReasons =
    orderLineStatusView === OrderLineStatusView.canceledBySeller
  const isShowingReturnDeclinedViewReasons =
    orderLineStatusView === OrderLineStatusView.returnDeclined

  let delayedRangeMessage: string | undefined
  if (
    orderLineStatusView === OrderLineStatusView.placedDelayed ||
    orderLineStatusView === OrderLineStatusView.confirmedDelayed
  ) {
    delayedRangeMessage = formatDateRange(
      orderLine.deliveryDetails.expected?.start
        ? new Date(orderLine.deliveryDetails.expected?.start)
        : null,
      orderLine.deliveryDetails.expected?.end
        ? new Date(orderLine.deliveryDetails.expected?.end)
        : null,
      market
    )
  }

  const isExtraInformationBold =
    !extraInformationNotBold.includes(orderLineStatusView)

  const isShowingDateRange = [
    OrderLineStatusView.confirmed,
    OrderLineStatusView.placed,
    OrderLineStatusView.shippedNotIntegratedWithCarrier,
    OrderLineStatusView.shippedReadyToShip,
    OrderLineStatusView.shippedOnItsWay,
    OrderLineStatusView.shippedOnItsWay
  ].includes(orderLineStatusView)

  if (isShowingDateRange) {
    const start =
      orderLine.deliveryDetails.revised?.start ??
        orderLine.deliveryDetails.expected?.start
        ? new Date(
          orderLine.deliveryDetails.revised?.start ??
          orderLine.deliveryDetails.expected?.start
        )
        : null
    const end =
      orderLine.deliveryDetails.revised?.start ??
        orderLine.deliveryDetails.expected?.end
        ? new Date(
          orderLine.deliveryDetails.revised?.end ??
          orderLine.deliveryDetails.expected?.end
        )
        : null
    extraInformationDate = formatDateRange(start, end, market)
  }
  const isShowingDeliveredAt = [
    OrderLineStatusView.shippedDeliveredFirst14Days,
    OrderLineStatusView.shippedDeliveredAfter14Days
  ].includes(orderLineStatusView)

  const isShowingEndDate = [
    OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfterEndDay,
    OrderLineStatusView.shippedDeliveredNotIntegratedWithCarrierAfter14DaysEndDay,
  ].includes(orderLineStatusView)

  if (isShowingDeliveredAt) {
    const deliveredAt = orderLine.trackingDetails?.[trackingIndex]?.deliveredAt
    if (deliveredAt) {
      extraInformationDate = formatDate(new Date(deliveredAt), market)
    } else if (orderLineStatusView === OrderLineStatusView.shippedDeliveredAfter14Days) {
      extraInformationDate = t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_DELIVERED')
      extraInformationText = ''
    } else {
      extraInformationText = undefined
    }
  }

  if (isShowingEndDate) {
    if (
      !!orderLine.deliveryDetails.revised?.end ||
      !!orderLine.deliveryDetails.expected?.end
    ) {
      extraInformationDate = formatDate(
        new Date(
          orderLine.deliveryDetails.revised?.end ??
          orderLine.deliveryDetails.expected?.end
        ),
        market
      )
    }
  }

  const isMetroSeller = orderLine.sellerDetails.id === config.METRO_ID

  const isShowingPlantCategoryInformation =
    orderLineStatusView === OrderLineStatusView.returnRequested &&
    orderLine.productDetails.isPlantCategory &&
    isMetroSeller

  const isShowingBulkyInformation =
    orderLineStatusView === OrderLineStatusView.returnRequested &&
    orderLine.productDetails.isBulky

  return (
    <div
      className="flex flex-col space-y-[6px] font-lato"
      data-testid="order-line-status-info"
    >
      <div>
        {labelClassName && label && (
          <span
            className={`rounded-full whitespace-nowrap pt-[6px] pb-[6px] pl-[10px] pr-[10px] font-bold text-base ${labelClassName} mr-[6px] w-auto`}
            data-testid="order-line-status-info-label"
          >
            {t(label)}
          </span>
        )}
        {extraInformationText === '' && !!extraInformationDate && (
          <span
            className=" text-primary-main text-base font-bold font-lato"
            data-testid="order-line-status-info-label-date"
          >
            {extraInformationDate}
          </span>
        )}

        {isShowingCancellationViewReasons && (
          <>
            <button
              className="text-blue-main cursor-pointer text-base bg-none border-none p-0 m-0 appearance-none"
              data-testid="order-line-status-view-reasons-cancellation"
              ref={showViewCancelReasonsButtonRef}
              onClick={() => {
                setShowReasonsModal(true)
              }}
              onKeyDown={handleEnterOrSpacePress(() =>
                setShowReasonsModal(true)
              )}
            >
              {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.VIEW_REASONS')}
            </button>
            {showReasonsModal && (
              <InfoModal
                title={t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.CANCELLATION_REASONS'
                )}
                onClose={() => {
                  setShowReasonsModal(false)
                  showViewCancelReasonsButtonRef?.current.focus()
                }}
              >
                <div className="flex flex-col mb-3 text-metro-blue-main text-base">
                  {orderLine.cancellationDetails?.cancellationReason?.title}
                </div>
              </InfoModal>
            )}
          </>
        )}
        {isShowingReturnDeclinedViewReasons && (
          <>
            <button
              className="text-blue-main cursor-pointer text-base bg-none border-none p-0 m-0 appearance-none"
              data-testid="order-line-status-view-reasons-return"
              ref={showViewRejectedReasonsButtonRef}
              onClick={() => {
                setShowReasonsModal(true)
              }}
              onKeyDown={handleEnterOrSpacePress(() =>
                setShowReasonsModal(true)
              )}
            >
              {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.VIEW_REASONS')}
            </button>
            {showReasonsModal && (
              <InfoModal
                title={t(
                  'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN_REJECTED_REASONS'
                )}
                onClose={() => {
                  setShowReasonsModal(false)
                  showViewRejectedReasonsButtonRef?.current.focus()
                }}
              >
                <div className="flex flex-col mb-3 text-metro-blue-main text-base">
                  {orderLine.returnDetails.returnResolution}
                </div>
              </InfoModal>
            )}
          </>
        )}
      </div>
      {delayedRangeMessage && (
        <div
          data-testid="order-line-status-info-delayed"
          className="text-[#B25C00] text-base"
        >
          {t(
            'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED.EXPECTED'
          )}{' '}
          <span className="font-bold">
            {delayedRangeMessage} (
            {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.DELAYED')})
          </span>
        </div>
      )}
      {extraInformationText !== undefined && extraInformationText !== '' && (
        <span
          data-testid="order-line-status-info-extra"
          className={cn(
            'text-primary-main text-base leading-[21px] font-lato',
            isExtraInformationBold && isMobile && 'font-bold'
          )}
        >
          {t(extraInformationText)}{' '}
          <span className="font-bold">{extraInformationDate}</span>
        </span>
      )}

      {isShowingBulkyInformation && !isShowingPlantCategoryInformation && (
        <span
          className="text-primary-main text-base leading-[21px] font-lato font-bold"
          data-testid="order-line-status-info-extra"
        >
          <span data-testid="order-line-status-return-content">
            {t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT')}
          </span>{' '}
          <button
            data-testid="order-line-status-view-instructions"
            className="text-secondary-main cursor-pointer underline bg-none border-none p-0 m-0 appearance-none"
            onClick={() => setShowReturnInstructionsSidebar(true)}
          >
            {t(
              'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.VIEW_INSTRUCTIONS'
            )}
          </button>
        </span>
      )}

      {isShowingPlantCategoryInformation && (
        <span
          className="text-primary-main text-base leading-[21px] font-lato font-bold"
          data-testid="order-line-status-return-content-plant"
        >
          <span>
            {t(
              'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT.PLANT'
            )}
          </span>
        </span>
      )}

      {showReturnInstructionsSidebar && (
        <ReturnInstructionsSidebar
          onClose={() => setShowReturnInstructionsSidebar(false)}
        />
      )}
    </div>
  )
}
