import { Chat } from '@metromarkets/message-center-sdk'
import { useEffect, useRef } from 'react'

import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { useChatList } from './useChatlist'

/**
 * Custom hook to determine if a seller conversation is open for a given order.
 * <PERSON>les chat fetching and memoizes the fetch effect.
 *
 * @param orderId - The order ID to fetch chats for
 * @returns isOpenSellerConversation - function to check if a conversation is open for a sellerId
 */
export function useIsSellerConversation(orderId: string) {
  const chatsFetchedRef = useRef(false)
  const { fetchChats, filteredChats } = useChatList({ orderId })
  const isMessageCenterC2AFeatureEnabled = useFeatureFlag(
    FeatureFlag.FF_CCS_MESSAGE_CENTER_ORDER_C2A
  )

  useEffect(() => {
    if (!chatsFetchedRef.current && isMessageCenterC2AFeatureEnabled) {
      fetchChats()
      chatsFetchedRef.current = true
    }
  }, [fetchChats])

  if (!isMessageCenterC2AFeatureEnabled) {
    return false
  }

  /**
   * Checks if a conversation is open for the given sellerId.
   * @param sellerId - The seller's organization id
   * @returns boolean
   */
  const isOpenConversation =
    ({ chats, orderId }: { chats: Chat[]; orderId: string }) =>
    (organizationId: string) => {
      return chats?.some(
        (chat) =>
          chat.order.id === orderId &&
          chat.seller.organization.id === organizationId
      )
    }

  const isOpenSellerConversation = (sellerId: string) =>
    isOpenConversation({ chats: filteredChats, orderId })(sellerId)

  return isOpenSellerConversation
}
