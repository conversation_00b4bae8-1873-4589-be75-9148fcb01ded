import { act, renderHook } from '@testing-library/react-hooks'

import { useIsSellerConversation } from '../useIsSellerConversation'

jest.mock('@metromarkets/message-center-sdk', () => ({
  ChatBadgeStatus: { NEW: 'NEW' }
}))

jest.mock('../useChatlist')

jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    appApiBaseUrl: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))

const mockFetchChats = jest.fn()

const getMockUseChatList = (filteredChats = []) => ({
  fetchChats: mockFetchChats,
  filteredChats
})

describe('useIsSellerConversation', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('returns true if a chat exists for the given orderId and sellerId', () => {
    const orderId = 'order1'
    const sellerId = 'seller1'
    const filteredChats = [
      {
        order: { id: orderId },
        seller: { organization: { id: sellerId } }
      }
    ]
    require('../useChatlist').useChatList.mockReturnValue(
      getMockUseChatList(filteredChats)
    )
    const { result } = renderHook(() => useIsSellerConversation(orderId))
    expect(result.current(sellerId)).toBe(true)
  })

  it('returns false if no chat exists for the given sellerId', () => {
    const orderId = 'order1'
    const sellerId = 'seller1'
    const filteredChats = [
      {
        order: { id: orderId },
        seller: { organization: { id: 'otherSeller' } }
      }
    ]
    require('../useChatlist').useChatList.mockReturnValue(
      getMockUseChatList(filteredChats)
    )
    const { result } = renderHook(() => useIsSellerConversation(orderId))
    expect(result.current(sellerId)).toBe(false)
  })

  it('returns false if chat list is empty', () => {
    const orderId = 'order1'
    const sellerId = 'seller1'
    require('../useChatlist').useChatList.mockReturnValue(
      getMockUseChatList([])
    )
    const { result } = renderHook(() => useIsSellerConversation(orderId))
    expect(result.current(sellerId)).toBe(false)
  })

  it('calls fetchChats only once (effect runs once)', () => {
    const orderId = 'order1'
    require('../useChatlist').useChatList.mockReturnValue(
      getMockUseChatList([])
    )
    renderHook(() => useIsSellerConversation(orderId))
    expect(mockFetchChats).toHaveBeenCalledTimes(1)
  })
})
