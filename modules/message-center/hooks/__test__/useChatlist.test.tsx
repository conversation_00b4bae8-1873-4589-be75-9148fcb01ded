import { waitFor } from '@testing-library/react'
import { act, renderHook } from '@testing-library/react-hooks'
import * as routerModule from 'next/router'

import { getProxyEndpoint } from '@core/constants/api.constants'
import { useMarket } from '@core/redux/features/market'
import { http } from '@core/services/http/http-request'

import { useChatList } from '../useChatlist'

jest.mock('@core/services/http/http-request')
jest.mock('@core/constants/api.constants')
jest.mock('@core/redux/features/market')
jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    appApiBaseUrl: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))
jest.mock('@core/utils/getBaseHref', () => ({
  getBaseHref: jest.fn(() => 'mock-base')
}))

jest.mock('@metromarkets/message-center-sdk', () => ({
  Order: undefined,
  ChatBadgeStatus: { NEW: 'NEW' }
}))
jest.mock('../useOrder', () => ({
  useOrder: jest.fn(() => ({
    order: {
      orderNumber: '123',
      buyerDetails: {
        firstName: 'John',
        lastName: 'Doe'
      }
    }
  }))
}))
jest.mock('@modules/message-center/hooks/useIsSellerConversation', () => ({
  useIsSellerConversation: () => jest.fn(() => true)
}))

describe('useChatList', () => {
  const market = 'DE'
  let pushMock: jest.Mock

  beforeEach(() => {
    pushMock = jest.fn()
    useMarket.mockReturnValue(market)
    getProxyEndpoint.mockReturnValue('mocked-endpoint')
    jest.spyOn(routerModule, 'useRouter').mockReturnValue({
      query: { orderId: undefined },
      push: pushMock
    } as any)
  })

  it('should fetch chats and update state', async () => {
    const mockChats = [
      {
        id: '1',
        order: {
          id: 'order1',
          orderNumber: '123'
        },
        isRead: false,
        isSelected: true,
        seller: { organization: { id: 'org1' } }
      }
    ]
    http.mockResolvedValue({ data: { data: mockChats } })

    const { result } = renderHook(() =>
      useChatList({
        orderId: 'order1',
        organizationId: 'org1'
      })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    expect(result.current.filteredChats).toEqual(mockChats)
  })

  it('should handle error during fetchChats', async () => {
    http.mockRejectedValue(new Error('Error fetching chat list'))

    const consoleErrorSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    const { result } = renderHook(() =>
      useChatList({
        orderId: 'order1',
        organizationId: 'org1'
      })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error fetching chat list',
      expect.any(Error)
    )

    consoleErrorSpy.mockRestore()
  })

  it('should select chat and update state', async () => {
    const mockChats = [
      {
        id: '1',
        order: {
          id: 'order1',
          orderNumber: '123'
        },
        isRead: false,
        isSelected: true,
        seller: { organization: { id: 'org1' } }
      },
      {
        id: '2',
        order: {
          id: 'order2',
          orderNumber: '1234'
        },
        isRead: false,
        isSelected: true,
        seller: { organization: { id: 'org1' } }
      }
    ]
    http.mockResolvedValue({ data: { data: mockChats } })

    const { result } = renderHook(() =>
      useChatList({
        orderId: 'order1',
        organizationId: 'org1'
      })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    await act(async () => {
      await result.current.selectChat(mockChats[1])
    })

    expect(pushMock).toHaveBeenCalledWith(
      expect.stringContaining('/message-center/order2/org1')
    )
  })

  it('should filter chats based on search text', async () => {
    const mockChats = [
      {
        id: '1',
        isRead: false,
        order: { id: 'order1', orderNumber: '123' },
        seller: { organization: { id: 'org1' } }
      },
      {
        id: '2',
        isRead: true,
        order: { id: 'order2', orderNumber: '456' },
        seller: { organization: { id: 'org1' } }
      }
    ]
    http.mockResolvedValue({ data: { data: mockChats } })

    const { result } = renderHook(() =>
      useChatList({
        orderId: 'order1',
        organizationId: 'org1'
      })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    act(() => {
      result.current.onSearch('123')
    })

    expect(result.current.filteredChats).toEqual([mockChats[0]])

    act(() => {
      result.current.onSearch('')
    })

    expect(result.current.filteredChats).toEqual(mockChats)
  })

  it('should update chat selection state when orderId matches a chat', async () => {
    const mockChats = [
      {
        id: '1',
        isRead: true,
        order: { id: 'order1', orderNumber: '123' },
        seller: { organization: { id: 'org1' } }
      },
      {
        id: '2',
        isRead: true,
        order: { id: 'order2', orderNumber: '456' },
        seller: { organization: { id: 'org1' } }
      }
    ]
    http.mockResolvedValue({ data: { data: mockChats } })

    const { result } = renderHook(() =>
      useChatList({
        orderId: 'order2',
        organizationId: 'org1'
      })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    // Check that the chat with matching orderId is selected
    expect(result.current.filteredChats[0].isSelected).toBe(false)
    expect(result.current.filteredChats[1].isSelected).toBe(true)
  })

  it('should return early if selected chat is already selected', async () => {
    const mockChats = [
      {
        id: '1',
        order: { id: 'order1', orderNumber: '123' },
        isRead: false,
        isSelected: true,
        seller: { organization: { id: 'org1' } }
      }
    ]
    http.mockResolvedValue({ data: { data: mockChats } })

    const { result } = renderHook(() =>
      useChatList({
        orderId: 'order1',
        organizationId: 'org1'
      })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    const selected = result.current.filteredChats[0]

    await act(async () => {
      await result.current.selectChat(selected)
    })

    // push should NOT be called again
    expect(pushMock).not.toHaveBeenCalled()
  })

  it('should default organizationId to an empty string when undefined', async () => {
    const mockOrder = { id: 'order1', orderNumber: '123' }
    const mockOrganizationId = undefined

    const { result } = renderHook(() =>
      useChatList({ orderId: mockOrder.id, organizationId: mockOrganizationId })
    )

    await act(async () => {
      await result.current.fetchChats()
    })

    expect(result.current.selectedChat?.seller.organization.id).toBe('')
  })
})
