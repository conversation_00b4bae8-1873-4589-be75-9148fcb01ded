import { Chat, ChatBadgeStatus } from '@metromarkets/message-center-sdk'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

import { getProxyEndpoint } from '@core/constants/api.constants'
import { APP_MESSAGE_CENTER } from '@core/constants/routesConstants'
import { useMarket } from '@core/redux/features/market'
import { http } from '@core/services/http/http-request'

import { useOrder } from './useOrder'

interface Props {
  orderId: string
  organizationId?: string
}

export const useChatList = ({ orderId, organizationId }: Props) => {
  const router = useRouter()
  const { order } = useOrder(orderId as string, organizationId)

  const market = useMarket()
  const [chats, setChats] = useState<Chat[]>([])
  const [filteredChats, setFilteredChats] = useState<Chat[]>([])
  const [selectedChat, setSelectedChat] = useState(
    chats?.find(
      (chat) =>
        chat.order.id === orderId &&
        chat.seller.organization.id === organizationId
    )
  )

  const fetchChats = async (isUpdate?: boolean) => {
    try {
      const url = getProxyEndpoint(market, 'message-center/get-chats')
      const { data: chats }: { data: { data: Chat[] } } = await http(
        url,
        { method: 'get' },
        { market }
      )
      setChats(chats.data)
      setFilteredChats(chats.data)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error fetching chat list', error)
    }
  }

  const selectChat = async (chatSelected: Chat): Promise<void> => {
    if (chatSelected.id === selectedChat?.id) {
      return
    }
    const updatedChat = { ...chatSelected, isRead: true, isSelected: true }
    setChats((prevChats) =>
      prevChats?.map((chat) =>
        chat.id === chatSelected.id
          ? { ...updatedChat }
          : { ...chat, isSelected: false }
      )
    )

    setSelectedChat(updatedChat)

    // TODO create getMessageCenterRelativePath (based on below) and use it to redirect from order history without pare reload
    router.push(
      `/${APP_MESSAGE_CENTER}/${chatSelected.order.id}/${chatSelected.seller.organization.id}`
    )
  }

  const onSearch = (searchText: string) => {
    if (searchText) {
      setFilteredChats(
        chats.filter((chat) => chat.order.orderNumber === searchText)
      )
    } else {
      setFilteredChats(chats)
    }
  }

  useEffect(() => {
    setChatList(chats)
  }, [orderId, chats, order?.orderNumber])

  const setChatList = (data: Chat[]) => {
    const selectedChat = data?.find(
      (chat) =>
        chat.order.id === orderId &&
        chat.seller.organization.id === organizationId
    )
    if (selectedChat || !order) {
      setFilteredChats(
        data?.map((chat) => ({
          ...chat,
          isSelected: selectedChat?.id === chat.id
        })) ?? []
      )
      setSelectedChat(selectedChat)
    } else {
      // TODO GM: new chat object
      const newChat: Chat = {
        buyer: order.buyer,
        inactive: false,
        lastMessageAt: '',
        lastMessageByUserType: 'BUYER',
        needsReply: false,
        overSLA: false,
        order: {
          id: orderId,
          orderNumber: order.orderNumber ?? '',
          salesChannel: market
        },
        seller: {
          organization: {
            id: organizationId ?? '',
            shopName: ''
          },
          accounts: []
        },
        isRead: false,
        createdAt: new Date().toISOString(),
        id: 'new',
        status: ChatBadgeStatus.NEW,
        subject: '',
        initiatorUserType: 'BUYER',
        isSeen: false,
        isSelected: true
      }
      setFilteredChats([newChat, ...data])
      setSelectedChat(newChat)
    }
  }

  return {
    filteredChats,
    selectedChat,
    order,
    fetchChats,
    selectChat,
    onSearch
  }
}
