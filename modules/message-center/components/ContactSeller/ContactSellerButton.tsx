import { useAccountTranslation } from '@core/hooks/useTranslation'

import { OrderLine } from '@modules/account/components/orders-history/types'
import { Button } from '@modules/shared/components'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

import { useContactSeller } from './useContactSeller'

interface Props {
  orderId: string
  orderNumber: string
  orderLine: OrderLine
  children?: React.ReactNode
  className?: string
  dataTestId?: string
}

export const ContactSellerButton = ({
  orderId,
  orderNumber,
  orderLine,
  children,
  className = '',
  dataTestId = 'contact-seller-button'
}: Props) => {
  const { t } = useAccountTranslation()
  const {
    handleContactSeller,
    isCheckingEligibility,
    isMessageCenterC2AFeatureEnabled
  } = useContactSeller(orderId, orderNumber, orderLine)

  if (!isMessageCenterC2AFeatureEnabled) {
    return null
  }

  return (
    <Button
      variant={BUTTON_VARIANTS.infoAccessible}
      className={`w-full text-regular font-semibold font-lato min-h-[40px] ${className}`}
      onClick={handleContactSeller}
      data-testid={dataTestId}
      disabled={isCheckingEligibility}
      rounded
    >
      {children ||
        t('PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER')}
    </Button>
  )
}
