import { useTranslation } from 'next-i18next'
import { FC } from 'react'

import { config } from '@core/config'
import { PriceType } from '@core/types'

import { ImageAndPromotionUI } from '@modules/product/components/product-card/productCardSubComponents'
import {
  ProductIndexItem,
  ProductIndexOffer,
  ProductIndexRegionInfo
} from '@modules/product/types'
import { Item } from '@modules/product/types/spi'
import { BlackDealLabel, LinkButton } from '@modules/shared/components'
import { cls } from '@modules/shared/utils'
import { mapAttributesToObject } from '@modules/shared/utils/EnergyEfficiencyLabel.utils'
import { getImageSrcSet } from '@modules/shared/utils/imageUtils'

import { ProductAttributeLabelsV2 } from '../product-attributes-labels/V2'
import { BuyAgainCardPriceUI } from './product-buy-again-sub-components'
import { BuyAgainAddToCartUI } from './product-buy-again-sub-components'

interface Props {
  productIndexItem: ProductIndexItem
  priceType: PriceType
  productViewUrl: string
  productIndexOffer: ProductIndexOffer
  vatRegionInfo: ProductIndexRegionInfo
  addedToCart: boolean
  onAddToCart: () => void
  onProductClick: () => void
  isBlackDealEnabled: boolean
}

export const ProductBuyAgainCardUI: FC<Props> = ({
  productIndexItem,
  priceType,
  productViewUrl,
  productIndexOffer,
  vatRegionInfo,
  addedToCart,
  onAddToCart,
  onProductClick,
  isBlackDealEnabled
}) => {
  const { t } = useTranslation()
  const productCatalogPlaceholder = `${config.cdnBaseUrl}/images/product-catalog-placeholder.png`
  const hasPromotion = productIndexOffer?.promotion
  const IMAGE_HEIGHT = 148
  const IMAGE_WIDTH = 148

  return (
    <div
      className="mdMinus:basis-[230px] flex-grow-0 flex-shrink-0 box-border !h-auto"
      data-test-id="product_card_container"
    >
      <div className="w-40 md:min-w-40 md:w-full md:max-w-[230px] md:p-3.5 md:pt-0 p-3 pt-0 relative flex flex-col justify-items-stretch h-full gap-2 [&_a]:h-full">
        <LinkButton
          url={productViewUrl}
          className="h-full"
          variant={null}
          border={null}
          isNative={true}
        >
          <button
            className="not-prose bg-white-main flex flex-col h-full w-full text-left font-lato"
            data-testid="product_card"
            onClick={onProductClick}
          >
            <ul className="absolute z-[2] h-6 flex gap-1.5" aria-hidden="true">
              {isBlackDealEnabled ? (
                <BlackDealLabel />
              ) : (
                hasPromotion && (
                  <li
                    className="p-1.5 bg-metro-red text-white-main text-[13px] leading-[13px] font-semibold rounded-bl-sm rounded-br-sm"
                    data-testid="product_promotion_label"
                  >
                    {t('CORE.ALL_PAGES.PRODUCT_PROMO.LABEL')}
                  </li>
                )
              )}
            </ul>
            <div className="w-full relative">
              <figure
                className="w-40 h-40 relative mx-auto z-1 mb-1 md:w-[148px] md:h-[148px]"
                aria-hidden="true"
              >
                <ImageAndPromotionUI
                  image={
                    productIndexItem?.images[0]?.url
                      ? getImageSrcSet(
                          productIndexItem?.images[0]?.url,
                          IMAGE_HEIGHT,
                          IMAGE_WIDTH
                        )
                      : productCatalogPlaceholder
                  }
                  index={0}
                  onLoadingComplete={null}
                  fullWidth={true}
                  testId="product_image"
                  altText={productIndexItem?.name}
                />
              </figure>
            </div>

            <p
              data-testid="product_name"
              className={cls(
                `${'min-h-12 text-base font-normal text-metro-blue-tint-20 break-all leading-5 line-clamp-2 pt-1 mb-1 hover:text-blue-main md:pt-2 md:mb-2 xs:line-clamp-2'}`
              )}
            >
              {productIndexItem?.name}
            </p>
            <BuyAgainCardPriceUI
              vatRegionInfo={vatRegionInfo}
              priceType={priceType}
            />
          </button>
        </LinkButton>
        {productIndexItem?.attributes && (
          <div className="absolute top-32 left-3.5 z-[11] [&_img]:!mt-0.5">
            <ProductAttributeLabelsV2
              productItem={
                {
                  ...productIndexItem,
                  attributes: mapAttributesToObject(
                    productIndexItem?.attributes
                  )
                } as Item
              }
              isProductCard={true}
              variant="small"
            />
          </div>
        )}
        <BuyAgainAddToCartUI
          isAddedToCart={addedToCart}
          onAddToCart={onAddToCart}
        />
      </div>
    </div>
  )
}
