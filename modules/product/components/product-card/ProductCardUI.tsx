import { config } from '@core/config'
import { useSearchBarFocus } from '@core/hooks/useSearchBarFocus'
import { useFeatureFlag } from '@core/ssr/featureFlag/featureFlag.context'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { LinkButton } from '@shared/components'

import { BestOfferLabelUI } from '@modules/product/components/product-card/productCardSubComponents/BestOfferLabelUI'
import { MsrpDiscountLabelUI } from '@modules/product/components/product-card/productCardSubComponents/MsrpDiscountLabelUI'
import { ProductBlackDeaLabelUI } from '@modules/product/components/product-card/productCardSubComponents/ProductBlackDeaLabelUI'
import {
  CarouselProduct,
  TopProductAttributes
} from '@modules/product/types/product.type'
import { shouldShowScaleEnergyLabel } from '@modules/product/utils'
import { getMapEnergyEfficiencyVersion } from '@modules/product/utils/getMapEnergyEfficiencyVersion.util'
import { cls } from '@modules/shared/utils'

import {
  EnergyEfficiencyUI,
  ImageAndPromotionUI
} from './productCardSubComponents'
import { ProductLimitedStockLabelUI } from './productCardSubComponents/ProductLimitedStockLabelUI'
import { PricePerUnit } from './productCardSubComponents/price-per-unit'
import { ProductPrice } from './productCardSubComponents/product-price'

export interface ProductCardUIProps {
  onProductClickGTMEvent: () => void
  openEecDocument: (arg0: string) => Promise<void>
  index: number
  onLoadingComplete?: (position: number) => void
  product: CarouselProduct
  fromHomePage: boolean
  isBlackDealsEnabled: boolean
  productViewUrl: string
  isLimitedStock: boolean
  isBlackDealProduct: boolean
  containerClassnames?: string
}

export const ProductCardUI = ({
  onProductClickGTMEvent,
  openEecDocument,
  index,
  onLoadingComplete,
  product,
  fromHomePage,
  isBlackDealsEnabled,
  productViewUrl,
  isLimitedStock,
  isBlackDealProduct,
  containerClassnames
}: ProductCardUIProps): JSX.Element => {
  const { searchBarFocus } = useSearchBarFocus()
  const shouldShowEnergyScale = shouldShowScaleEnergyLabel(
    null,
    product.categories
  )

  const productCatalogPlaceholder = `${config.cdnBaseUrl}/images/product-catalog-placeholder.png`

  const { energyEfficiencyClass, energyEfficiencyRange, energyLabel } =
    product.attributes || ({} as TopProductAttributes)

  const { energyEfficiencyColor, energyEfficiencyVersion } =
    getMapEnergyEfficiencyVersion(product.attributes)
  const isExternalPDP = useFeatureFlag(FeatureFlag.FF_EXTERNAL_PDP)
  return (
    <>
      <LinkButton
        url={productViewUrl}
        className="h-full"
        variant={null}
        border={null}
        isNative={!isExternalPDP}
      >
        <div
          className={cls(`
            ${'flex flex-col px-16px pt-24px pb-10px hover:cursor-pointer h-full'}
            ${searchBarFocus && 'pointer-events-none'}
            ${containerClassnames ?? ''}
          `)}
          data-testid="product_card"
          onClick={() => onProductClickGTMEvent()}
        >
          <div className="relative flex h-[148px] w-auto flex-col items-center justify-center">
            <ImageAndPromotionUI
              image={product.image || productCatalogPlaceholder}
              index={index}
              onLoadingComplete={onLoadingComplete}
              altText={product?.name}
            />
          </div>

          <div
            className={cls(
              `${'flex h-full mt-[30px] flex-col text-primary-main'}`
            )}
          >
            <div className="flex my-8px">
              {isBlackDealsEnabled && isBlackDealProduct && (
                <ProductBlackDeaLabelUI />
              )}
              {product?.bestOffer?.referencePrice?.discountPercentage &&
                !(isBlackDealsEnabled && isBlackDealProduct) && (
                  <MsrpDiscountLabelUI
                    msrpDiscount={
                      product.bestOffer.referencePrice?.discountPercentage
                    }
                  />
                )}
              {!product?.bestOffer?.referencePrice?.discountPercentage &&
                product?.bestOffer?.promotion &&
                fromHomePage &&
                !(isBlackDealsEnabled && isBlackDealProduct) && (
                  <BestOfferLabelUI />
                )}
            </div>
            <div
              className={cls(`
              ${'prose overflow-hidden text-ellipsis whitespace-normal break-all antialiased hover:text-info-main'}
              ${'text-[16px] font-normal leading-6 tracking-normal text-metro-blue-main'}
              ${
                energyEfficiencyClass || energyEfficiencyRange
                  ? 'mb-30px xs:line-clamp-1 md:mb-0 lg:line-clamp-2'
                  : 'mb-30px xs:line-clamp-2 md:mb-0 lg:line-clamp-3'
              }
              ${product?.bestOffer?.referencePrice?.netPrice && 'xs:mb-[3px]'}
            `)}
              data-testid="product-name"
            >
              {product.name}
            </div>

            <div className="bottom-0 mt-auto">
              <EnergyEfficiencyUI
                energyEfficiencyImage={energyLabel}
                energyEfficiencyColor={energyEfficiencyColor}
                energyEfficiencyVersion={energyEfficiencyVersion}
                energyEfficiencyLabel={energyEfficiencyClass}
                openEecDocument={openEecDocument}
                shouldShowEnergyScale={shouldShowEnergyScale}
              />

              <ProductPrice
                bestOffer={product.bestOffer}
                productViewUrl={productViewUrl}
              />

              <span className="md:hidden">
                <PricePerUnit bestOffer={product.bestOffer} />
              </span>
            </div>
          </div>

          {isLimitedStock && <ProductLimitedStockLabelUI />}
        </div>
      </LinkButton>
    </>
  )
}
