import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

import { NUMBER_1 } from '@core/constants/numbers'
import { useIsInViewport } from '@core/hooks/useInViewport'

type Props = {
  image: string | StaticImageData
  index: number
  onLoadingComplete?: (position: number) => void
  fullWidth?: boolean
  testId?: string
  isSponsorProduct?: boolean
  className?: string
  altText?: string
  loading?
}

const loadSrc = ({ src }) => src

export const ImageAndPromotionUI = ({
  image,
  index,
  onLoadingComplete,
  fullWidth = false,
  testId,
  isSponsorProduct,
  className,
  loading,
  altText
}: Props) => {
  const ref = useRef()
  const { isInViewport } = useIsInViewport(ref, {
    threshold: NUMBER_1,
    isConstantlyObserved: isSponsorProduct
  })
  const [loadCompleteCalled, setLoadCompleteCalled] = useState(false)

  const handleOnLoadingComplete = () => {
    if (isInViewport && !loadCompleteCalled) {
      setLoadCompleteCalled(true)
      return onLoadingComplete && onLoadingComplete(index)
    }
  }

  useEffect(() => {
    handleOnLoadingComplete()
  }, [isInViewport])

  return (
    <Image
      className={className}
      ref={ref}
      loader={loadSrc}
      alt={altText}
      src={image}
      layout="fill"
      objectFit="contain"
      unoptimized={true}
      onLoadingComplete={handleOnLoadingComplete}
      blurDataURL="URL"
      placeholder="blur"
      data-testid={testId}
      loading={loading}
    />
  )
}
