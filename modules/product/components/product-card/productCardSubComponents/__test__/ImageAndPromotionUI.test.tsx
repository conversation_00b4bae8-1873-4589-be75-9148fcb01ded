import { render, screen } from '@testing-library/react'

import '@core/hooks/__mocks__/intersectionObserver'
import { renderWithProviders } from '@core/utils/testing'

// eslint-disable-next-line no-restricted-imports
import { TopProducts } from '../../../__test__/fixtures'
import { ImageAndPromotionUI } from '../ImageAndPromotionUI'

describe('ImageAndPromotionUI Component tests: ', () => {
  const { image } = TopProducts[0]

  it('ImageAndPromotionUI shapshot test', () => {
    const { container } = renderWithProviders(
      <ImageAndPromotionUI image={image} index={1} />
    )
    expect(container).toMatchSnapshot()
  })

  it('should render image', () => {
    renderWithProviders(
      <ImageAndPromotionUI image={image} index={1} altText="product-image" />
    )
    const renderedImage = screen.getByRole('img') as HTMLImageElement
    expect(renderedImage.src).toBe(image)
    expect(renderedImage.alt).toBe('product-image')
  })
})
