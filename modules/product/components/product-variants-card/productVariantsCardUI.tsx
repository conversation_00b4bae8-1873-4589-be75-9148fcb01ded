import { clsx } from 'clsx'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'

import { config } from '@core/config'
import { NUMBER_0 } from '@core/constants/numbers'
import { useIsInViewport } from '@core/hooks'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { RootState } from '@core/redux/store'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { LinkButton } from '@shared/components'
import { usePingCriteoBeacons } from '@shared/hooks/usePingCriteoBeacons'
import { cls } from '@shared/utils'

import { ProductBlackDeaLabelUI } from '@modules/product/components/product-card/productCardSubComponents/ProductBlackDeaLabelUI'
import { CarouselProduct, TopProductAttributes } from '@modules/product/types'
import { ParsedCriteoResponse } from '@modules/product/types/criteo/viewItem'
import {
  isBlackDealProduct,
  pingProductCardBeacon
} from '@modules/product/utils'
import { CriteoPlacementBeacons } from '@modules/search/types/criteo-sponsored-search-response'
import { EnergyEfficiencyLabel } from '@modules/shared/components/EnergyEfficiencyLabel/EnergyEfficiencyLabel'
import {
  EnergyConfig,
  EnergyEfficiencyAttributes
} from '@modules/shared/components/EnergyEfficiencyLabel/types'

import { ImageAndPromotionUI } from '../product-card/productCardSubComponents'
import { Sponsorship } from '../sponsorship'
import {
  CardPrice,
  VariantsIndicator
} from './product-variants-card-components'

interface Props {
  onProductClickGTMEvent: () => void
  openEecDocument: (arg0: string) => Promise<void>
  product: CarouselProduct
  index: number
  onLoadingComplete?: (position: number) => void
  productViewUrl: string
  hasReferencePrice: boolean
  hasBasePriceOrMoreThanOneUnit: boolean
  shouldKeepColorSection?: boolean
  shouldKeepOtherVariantsSection?: boolean
  fromPLP?: boolean
  criteoPlacementBeacons?: CriteoPlacementBeacons
  lastSponsoredProductIndex?: number
  onLastSponsoredProductLoaded?: Function
  imageLoading?
  forceStrikeThroughPriceHeight?: boolean
  someProductHasBothTypes?: boolean
  mustOpenPDPInOtherTab?: boolean // @TODO: Remove once experiment is concluded
}

export function ProductVariantsCardUI({
  onProductClickGTMEvent,
  openEecDocument,
  product,
  index,
  onLoadingComplete,
  productViewUrl,
  hasReferencePrice,
  fromPLP = false,
  hasBasePriceOrMoreThanOneUnit,
  shouldKeepColorSection = false,
  shouldKeepOtherVariantsSection = false,
  criteoPlacementBeacons,
  lastSponsoredProductIndex,
  onLastSponsoredProductLoaded,
  imageLoading,
  forceStrikeThroughPriceHeight,
  someProductHasBothTypes,
  mustOpenPDPInOtherTab = false
}: Props) {
  const { pingBeaconUrlWithCookiesConsentCheck } = usePingCriteoBeacons()
  const cardRef = useRef(null)
  const { isInViewport } = useIsInViewport(cardRef, {
    threshold: 0.5,
    isConstantlyObserved: false
  })
  const { t } = useTranslation()
  const productCatalogPlaceholder = `${config.cdnBaseUrl}/images/product-catalog-placeholder.png`
  const { energyLabel, energy_config = {} as EnergyConfig } =
    product.attributes || ({} as TopProductAttributes)

  const [currentImage, setCurrentImage] = useState(product?.image)

  const defaultLabel =
    energy_config?.energy_efficiency_label_config?.defaultAttribute

  const energyEfficiencyRange =
    energy_config?.energy_efficiency_label_config?.range

  const energyEfficiencyClass = energy_config?.[defaultLabel]

  const isExternalPDP = useFeatureFlag(FeatureFlag.FF_EXTERNAL_PDP)
  const carouselCardClasses = 'flex-col w-[160px] md:max-w-[230px]'
  const plpCardClasses =
    'md:flex-col w-full md:max-w-full hover:shadow-plpProductCard'

  const criteoURLs: ParsedCriteoResponse = useSelector(
    (state: RootState) => state.product?.parsedCriteoViewItem
  )

  const isBlackDealsEnabled = useFeatureFlag(
    FeatureFlag.FF_BLACK_FRIDAY_COUNTDOWN
  )

  const namedTestTarget =
    product?.name && typeof product?.name?.replaceAll === 'function'
      ? `product-card-${product?.name?.replaceAll(' ', '-')}`
      : ''

  const blackDealProduct: boolean = isBlackDealProduct(product?.tags)

  useEffect(() => {
    if (product.sponsoredProductIndex === lastSponsoredProductIndex) {
      pingBeaconUrlWithCookiesConsentCheck(criteoPlacementBeacons?.OnLoadBeacon)
      if (
        onLastSponsoredProductLoaded &&
        typeof onLastSponsoredProductLoaded === 'function'
      ) {
        onLastSponsoredProductLoaded()
      }
    }
  }, [])

  useEffect(() => {
    if (fromPLP && product?.sponsor && isInViewport) {
      pingProductCardBeacon(
        product?.OnViewBeacon,
        product?.sponsoredProductIndex,
        criteoPlacementBeacons?.OnViewBeacon,
        pingBeaconUrlWithCookiesConsentCheck
      )
    }
  }, [criteoPlacementBeacons, isInViewport])

  // Criteo onclick becaon
  const handleProductCardClick = (criteoURLs) => {
    if (criteoURLs?.[product?.id]) {
      pingBeaconUrlWithCookiesConsentCheck(
        criteoURLs?.[product?.id]?.OnClickBeacon
      )
    } else if (product?.OnClickBeacon) {
      pingBeaconUrlWithCookiesConsentCheck(product?.OnClickBeacon)
    }
    onProductClickGTMEvent()
  }

  const EECAttributes: EnergyEfficiencyAttributes = {
    energyLabel: energyLabel,
    energyEfficiencyRange: {
      from: energyEfficiencyRange?.max,
      to: energyEfficiencyRange?.min
    },
    energy_config: energy_config
  }
  return (
    <LinkButton
      aria-label={`${product.name} product card`}
      url={productViewUrl}
      className="w-full h-full text-left"
      variant={null}
      border={null}
      isNative={!isExternalPDP}
      mustOpenPDPInOtherTab={mustOpenPDPInOtherTab}
      onClick={() => handleProductCardClick(criteoURLs)}
    >
      <article
        ref={cardRef}
        test-target="product-card"
        data-testid="product_card"
        className={clsx(
          'not-prose bg-white-main flex h-full md:min-w-[160px] md:w-full md:p-[14px] p-3 relative font-lato rounded-[2px]',
          fromPLP ? plpCardClasses : carouselCardClasses
        )}
      >
        <section className={'absolute top-0 h-[24px] flex gap-[6px] z-[5]'}>
          {!blackDealProduct && !hasReferencePrice && product.hasPromotion && (
            <div
              data-testid="promotion-label"
              className="p-1.5 bg-metro-red text-white-main text-[13px] leading-[13px] font-semibold rounded-bl-sm rounded-br-sm"
            >
              {t('CORE.ALL_PAGES.PRODUCT_PROMO.LABEL')}
            </div>
          )}

          {isBlackDealsEnabled && blackDealProduct && (
            <ProductBlackDeaLabelUI />
          )}

          {!!product?.attributes?.series && (
            <div
              data-testid="serie-label"
              className="prose antialiased font-lato text-[13px] font-bold h-24px bg-metro-blue-tint-95 px-8px leading-6 w-fit text-metro-blue-main rounded-bl-sm rounded-br-sm"
            >
              {t('CATALOG.CATALOG_PAGE.PRODUCT-CARD.SERIES')}
            </div>
          )}
        </section>

        <div className="flex-col">
          <div
            className={clsx(
              'relative',
              fromPLP ? 'md:w-full mr-3 md:mr-0' : 'w-full'
            )}
          >
            <figure
              test-target={`product-card-${product.mid}`}
              className={clsx(
                'w-[130px] h-[130px] relative mx-auto mb-1 md:w-[148px] md:h-[148px]',
                fromPLP && 'z-[4]'
              )}
            >
              <ImageAndPromotionUI
                className={'w-[130px] h-[130px]'}
                image={
                  currentImage || product?.image || productCatalogPlaceholder
                }
                index={index}
                onLoadingComplete={onLoadingComplete}
                fullWidth={true}
                isSponsorProduct={product?.sponsor}
                loading={imageLoading}
                altText={product?.name}
              />
            </figure>
            {energyEfficiencyClass && (
              <div
                className={clsx(
                  'absolute bottom-0 left-0',
                  fromPLP && ' z-[5]'
                )}
              >
                <EnergyEfficiencyLabel
                  attributes={EECAttributes}
                  variant={'small'}
                  hasToShowProductDataLink={false}
                />
              </div>
            )}
          </div>

          <VariantsIndicator
            product={product}
            shouldKeepColorSection={shouldKeepColorSection}
            shouldKeepSizeSection={shouldKeepOtherVariantsSection}
            onProductClickGTMEvent={onProductClickGTMEvent}
            someProductHasBothTypes={someProductHasBothTypes}
            onVariantMouseOver={(variantImage) => {
              setCurrentImage(variantImage)
            }}
            onVariantMouseLeave={() => {
              setCurrentImage(product?.image)
            }}
            mustOpenPDPInOtherTab={mustOpenPDPInOtherTab}
          />
        </div>

        <div
          className={clsx(
            `flex flex-col ${product?.sponsor && 'justify-between flex-grow w-full'}`,
            fromPLP && 'flex-auto xs-mdMinus:self-center'
          )}
        >
          <p
            test-target={namedTestTarget}
            data-testid="product-name"
            className={cls(`text-[14px] font-normal text-primary-main  leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'}
                ${
                  energyEfficiencyClass || energyEfficiencyRange
                    ? 'xs:line-clamp-1 lg:line-clamp-2'
                    : 'xs:line-clamp-2'
                }
                ${fromPLP ? 'xs:!line-clamp-none' : 'min-h-11 lg:line-clamp-2'}
              `)}
          >
            {product.name}
          </p>

          {fromPLP && product?.attributes?.series && (
            <span
              className={cls(`text-[14px] font-normal text-metro-blue-tint-20 break-all leading-5 pt-1 mb-2 hover:text-blue-main md:pt-2'}
                  ${
                    energyEfficiencyClass || energyEfficiencyRange
                      ? 'xs:line-clamp-1 lg:line-clamp-2'
                      : 'xs:line-clamp-2'
                  }
                  ${fromPLP ? 'xs:!line-clamp-none' : 'min-h-11 line-clamp-2'} `)}
            >
              {' '}
              {t('CATALOG.CATALOG_PAGE.PRODUCT-CARD.SERIES')}:{' '}
              {product?.attributes?.series}
            </span>
          )}
          <div data-testid="product-price-section" className="mt-auto">
            <CardPrice
              bestOffer={product.bestOffer}
              productViewUrl={productViewUrl}
              hasReferencePrice={hasReferencePrice}
              hasBasePriceOrMoreThanOneUnit={hasBasePriceOrMoreThanOneUnit}
              forceStrikeThroughPriceHeight={forceStrikeThroughPriceHeight}
            />
          </div>
          {product.sponsor && (
            <Sponsorship
              sponsorName={product?.sponsorName}
              sponsorshipPaidBy={product?.sponsorshipPaidBy}
              index={index}
            />
          )}
        </div>
      </article>
    </LinkButton>
  )
}
