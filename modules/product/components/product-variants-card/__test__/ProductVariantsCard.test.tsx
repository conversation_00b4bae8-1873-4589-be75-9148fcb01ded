import { fireEvent, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/router'

import '@core/hooks/__mocks__/intersectionObserver'
import { PopUPModalProvider } from '@core/hooks/usePopUpModal'
import { SearchBarFocusProvider } from '@core/hooks/useSearchBarFocus'
import { renderWithProviders } from '@core/utils/testing'

import { GaTrackingEventLabel } from '@modules/ga-tracking/constants'
import { TopProducts } from '@modules/product/mocks/top-products/top-products-mock'
import { pingBeconUrl } from '@modules/product/utils'

import { ProductVariantsCard } from '../'

const gtagEventMock = jest.fn()

jest.mock('@core/hooks/useGtag', () => ({
  useGtag: () => ({
    gtagEvent: gtagEventMock
  })
}))
jest.mock('@modules/product/utils/criteo.utils', () => ({
  pingBeconUrl: jest.fn()
}))
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

describe('Product variants card', () => {
  it('should render component', async () => {
    useRouter.mockReturnValue({
      query: { searchByPhoto: '12345678' }
    })
    const { container } = renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    expect(container).toMatchSnapshot()
  })

  it('should have promotion label when product hasPromotion is true and hasReferncePrice is false', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[1]}
            hasReferencePrice={false}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const promotionLabelElement = screen.getByTestId('promotion-label')
    expect(promotionLabelElement).toBeInTheDocument()
  })

  it('should not have promotion label when product hasReferncePrice is true', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[1]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const promotionLabelElement = screen.queryByTestId('promotion-label')
    expect(promotionLabelElement).not.toBeInTheDocument()
  })

  it('should have serie label when product has any variant', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const promotionLabelElement = screen.getByTestId('serie-label')
    expect(promotionLabelElement).toBeInTheDocument()
  })

  it('should have product image', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const image = screen.getByAltText(TopProducts[0]?.name)
    expect(image).toBeTruthy()
  })

  it('should have product title', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const title = screen.getByText(TopProducts[0].name)
    expect(title).toBeInTheDocument()
  })

  it('should render price section', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const productPriceSection = screen.getByTestId('product-price-section')
    expect(productPriceSection).toBeInTheDocument()
  })

  it('should call GA4 & criteo Beacon url event when clicking product card', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
            fromPDPRetailMedia={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>,
      {
        preloadedState: {
          product: {
            highlightedVariantImageUrl: '',
            generalInfoAccordion: true,
            promoStories: null,
            parsedCriteoViewItem: {
              '6f2c525f-32e5-4efb-a073-d790b5440648': {
                OnClickBeacon: 'click beacon url'
              },
              OnViewBeacon: ''
            }
          }
        }
      }
    )

    const product = screen.getByTestId('product_card')
    fireEvent.click(product)

    // 2 times because it calls old GA360 and GA4 events
    await waitFor(() => {
      expect(gtagEventMock).toHaveBeenCalledTimes(2)
    })

    await waitFor(() => {
      expect(gtagEventMock).toHaveBeenLastCalledWith(
        expect.objectContaining({
          ecommerce: expect.objectContaining({
            currency: 'EUR',
            item_list_name: 'PDP Carousel - sponsored'
          }),
          event: 'select_item'
        })
      )
    })
  })

  it('should render component from PLP page', async () => {
    const { container } = renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            fromPLP={true}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    expect(container).toMatchSnapshot()
  })

  it('should render component from HP page', async () => {
    renderWithProviders(
      <PopUPModalProvider>
        <SearchBarFocusProvider>
          <ProductVariantsCard
            position={1}
            index={0}
            product={TopProducts[0]}
            hasReferencePrice={true}
            hasBasePriceOrMoreThanOneUnit={true}
          />
        </SearchBarFocusProvider>
      </PopUPModalProvider>
    )

    const productCard = screen.getByTestId('product_card')
    fireEvent.click(productCard)

    await waitFor(() => {
      expect(gtagEventMock).toHaveBeenCalledTimes(4)
    })
  })
})
