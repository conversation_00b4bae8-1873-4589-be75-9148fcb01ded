{"HEADER.ALL_PAGES.YOUR_ASSORTMENT_BUTTON": "Your assortment", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.APPROVE_ACTION.TEXT": "Approved. The branch has been notified.", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.APPROVE.TEXT": "Approve", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.BILLING_ADDRESS.TEXT": "Billing Address", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.KVK.TEXT": "KVK", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.LEGAL.TEXT": "By clicking on “Approve” you are placing a binding order in your name and on your account for the content of the order request. Our <a href=\"{gtcLink}\" className=\"text-info-main\"> GTC </a> and our <a href=\"#\" className=\"text-info-main underline\" target=\"_blank\"> Privacy Policy </a> apply.", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.ORDER.TEXT": "Order", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.ORDERLINE_QUANTITY.TEXT": "Quantity", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.ORDERLINE_VAT_INCL.TEXT": "(incl. VAT)", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.REJECT_ACTION.TEXT": "Rejected. The branch has been notified.", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.REJECT.TEXT": "Reject", "PAGES.ACCOUNT.APPROVE_ORDERS.DETAILS.SHIPPING_ADDRESS.TEXT": "Shipping Address", "PAGES.ACCOUNT.APPROVE_ORDERS.EMPTY_PAGE.MESSAGE": "All clear! When you have new orders to approve, they will appear here.", "PAGES.ACCOUNT.APPROVE_ORDERS.EMPTY_PAGE.TITLE": "No orders to approve", "PAGES.ACCOUNT.APPROVE_ORDERS.LISTING.HEADER": "Approvals Overview", "PAGES.ACCOUNT.APPROVE_ORDERS.PLACE_HOLDER.MESSAGE": "Orders Approval is currently unavailable for your account. Please contact your manager to enable this feature.", "PAGES.ACCOUNT.APPROVE_ORDERS.PLACE_HOLDER.PAGE_TITLE": "Approve Orders", "PAGES.ACCOUNT.APPROVE_ORDERS.PLACE_HOLDER.TITLE": "Feature disabled", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.BRANCH_NAME.TEXT": "Branch", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.CREATED_AT.TEXT": "Order Date", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.DETAILS_LINK.TEXT": "See details", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.KVK_NUMBER.TEXT": "KVK Number", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.ORDER_NUMBER.TEXT": "Order Number", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.ORDER_TOTAL.TEXT": "Order Value", "PAGES.ACCOUNT.APPROVE_ORDERS.TABLE.COLUMN.REQUESTED_BY.TEXT": "Requested By", "PAGES.ACCOUNT.BUY_AGAIN.TITLE": "Buy Again", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.BODY_TEXT": "The following business will be removed from your account. Are you sure you would like to delete the following branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.DELETE_BRANCH_BUTTON_TEXT": "Delete branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.DELETE_SUCCESS_NOTIFICATION_TEXT": "Branch successfully removed from your account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.DELETE_BRANCH_MODAL.TITLE": "Delete a connect business", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.HEADING": "Add a branch to your company account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.INFO_TEXT_1": "To link a new branch to your company, you will require the company representative email and metro card number.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.INFO_TEXT_2": "The information below will be provided to the branch to identify where the invite is originating from.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.LABEL_AND_MODAL_HEADING.INVITE_BRANCH": "Invite a branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.ADD_BRANCH.LABEL.COMPANY_NAME": "Company Name", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.BRANCH_ADDED_NOTIFICATION_TEXT": "Branch successfully added to your account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.BODY_INFO_TEXT": "We found the following company, click confirm to invite and connect this company to your account.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.HEADING": "Confirm company details", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.ADDRESS": "Address", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.CONFIRM_BRANCH_DETAILS": "Confirm branch details", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.EMAIL": "Email", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.LABEL.VAT_ID": "VAT ID", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONFIRM_DETAILS_MODAL.VAT_ID.NOT_AVAILABLE": "Not available", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.HEADING": "Connected branches", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LABEL.BRANCH_NUMBER": "Branch number", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LABEL.CONTACT": "Contact", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LABEL.METRO_CARD": "Metro Card", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CONNECTED_BRANCHES.LOAD_MORE": "Show more branches", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CREATE_DIGITAL_ACCOUNT_MODAL.BODY_INFO_TEXT": "requires a digital account in order to continue. Confirm the account creation for this email to continue:", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CREATE_DIGITAL_ACCOUNT_MODAL.HEADING": "Create a digital account", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.CREATE_DIGITAL_ACCOUNT_MODAL.LABEL.CONFIRM_ACCOUNT_CREATION": "Confirm account creation", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.FORM.BUTTON": "Back", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.BUTTON.INVITE_BRANCH": "Invite branch", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.FOOTER_NOTE": "Please ask the company to sign up for a METRO card here if they don’t have one.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.HEADING": "Please fill out the following information", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.METRO_CARD_NUMBER": "METRO card number", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.METRO_CARD_NUMBER.ERROR.ALREADY_LINKED": "The company you are trying to add is already linked as a branch to your organisation.", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.REPRESENTATIVE_EMAIL": "Representative email", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.INVITE_BRANCH_MODAL.FORM.LABEL.REPRESENTATIVE_NAME": "Representative name", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.LABEL.EMAIL_ADDRESS": "Email Address", "PAGES.ACCOUNT.KEY_ACCOUNTS.REGISTER_BRANCH.PAGE_TITLE": "Registered branches", "PAGES.ACCOUNT.LOADING": "Loading...", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.BACK.BUTTON": "Back to orders", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.BODY": "Something went wrong while loading your order details. Please reload the page to try again.", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.RELOAD.BUTTON": "Reload", "PAGES.ACCOUNT.ORDER_DETAIL.NOT_FOUND.TITLE": "Couldn’t load order details", "PAGES.ACCOUNT.ORDER_HISTORY": "Close", "PAGES.ACCOUNT.ORDER_HISTORY.BUTTON.EMPLOYEE_SIGNOUT": "Employee sign out", "PAGES.ACCOUNT.ORDER_HISTORY.BUTTON.NEW_IMPERSONATION": "Start with a new customer", "PAGES.ACCOUNT.ORDER_HISTORY.BUY_AGAIN": "Buy Again", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL_PRODUCT": "Cancel product", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL_TITLE": "Cancel item", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL.CANCEL": "Do not cancel", "PAGES.ACCOUNT.ORDER_HISTORY.CANCEL.MODAL.CONFIRM": "Confirm Cancellation", "PAGES.ACCOUNT.ORDER_HISTORY.COMPANY": "Company:", "PAGES.ACCOUNT.ORDER_HISTORY.CONTACT_SELLER": "<PERSON>ller", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.DOCUMENTS": "Get documents", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.INVOICES": "Get invoice(s)", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.DOWNLOAD_ALL": "Download all", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.TITLE": "Documents for order", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.TITLE.CREDIT_NOTES": "Credit notes", "PAGES.ACCOUNT.ORDER_HISTORY.DOWNLOAD.MODAL.TITLE.INVOICES": "Invoices", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.BODY": "Something went wrong while loading your orders. Please reload the page to try again.", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.RELOAD": "Reload", "PAGES.ACCOUNT.ORDER_HISTORY.ERROR.TITLE": "Couldn’t load orders", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY": "Payment and delivery information", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY.BILLING_ADDRESS": "Billing address", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY.DELIVERY_ADDRESS": "Delivery address", "PAGES.ACCOUNT.ORDER_HISTORY.INFORMATION.PAYMENT_DELIVERY.PAID_BY": "Paid by", "PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.CONTACT": "Contact Customer Service", "PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.INFO": "You will be contacted by the service provider to arrange a date and time.", "PAGES.ACCOUNT.ORDER_HISTORY.INSTALLATION_SERVICES.INSTRUCTIONS": "View installation instructions", "PAGES.ACCOUNT.ORDER_HISTORY.NO_ORDERS.BODY": "You have not placed any orders yet. Place your first order to see the purchase details here.", "PAGES.ACCOUNT.ORDER_HISTORY.NO_ORDERS.CONTINUE_SHOPPING": "Continue shopping", "PAGES.ACCOUNT.ORDER_HISTORY.NO_ORDERS.TITLE": "No orders yet", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.CANCEL.ORDER": "Cancel All Items", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.CANCEL.ORDER.DIALOG.TITLE": "Cancel order #{orderNumber}?", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER": "<PERSON>ller", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER.OPTION.ENQUIRY": "Order enquiry", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.CONTACT_SELLER.OPTION.TIME": "Request Delivery Time", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.SEE_MESSAGES": "See Messages", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.TRACKING_INVALID": "Track id might be invalid", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.TRACKING_NOT_AVAILABLE": "Not available", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_BODY": " ", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.OTHER_ENQUIRY.EMAIL_SUBJECT": "Question about the order {orderNumber}", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_BODY": "Dear seller, %0D%0Dplease provide me with the tracking code for the order {orderNumber}. %0D%0DThank you", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.CONTACT_SELLER.REQUEST_DELIVERY_TIME.EMAIL_SUBJECT": "Request code for shipment tracking for order {orderNumber}", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY": "Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY_WITH": "Delivered with", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.CURBSIDE": "Curbside Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.PARCEL": "<PERSON><PERSON><PERSON>", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.PLACE_OF_USE": "Place of Use Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DELIVERY.STANDARD": "Standard Delivery", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.DISCOUNT": "Discount", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.EXCL_VAT": "excl. VAT", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.INCL_VAT": "incl, VAT", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.MULTIPLE.PACKAGES": "Multiple packages", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.ORDER_PLACED": "Order placed:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.PACKAGE": "Package", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.PRICE": "Price", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.QUANTITY": "Quantity", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.RETURN": "Return item", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.SOLD_BY": "Sold by", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.CANCELLATION_REASONS": "Cancellation reasons", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SECURITY": "Security Check Failed. This is to safeguard your account and ensure safe shopping. Contact support if needed.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.CANCELLED_SELLER": "Refund has been initiated. It might take several business days depending on your bank account.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.DELAYED": "Delayed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_BY": "Expected by", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_DELIVERY": "Expected delivery:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.EXPECTED_ON": "Delivered on", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PAID": "We are manually checking your payment. This is to safeguard your account and ensure safe shopping. Contact support if needed.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED": "We will communicate a new delivery estimation via e-mail when the item will be shipped.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.PLACED_DELAYED.EXPECTED": "Initial expected delivery:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REFUNDED": "Refund might take several business days depending on your bank account.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.REJECTED": "Contact your head office for more info", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.RETURNED": "We will proceed with the refund soon", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.EXTRA.SHIPPED_FAILED": "Please track your item or reach out to the seller", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.CANCELLED": "Order cancelled", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_DELAYED": "Order delayed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.ORDER_PLACED": "Order placed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.PAYMENT_FAILED": "Payment failed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.READY_SHIP": "Ready to ship", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REFUNDED": "Refunded", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.REJECTED": "Order rejected", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED": "Shipped", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_DELIVERED": "Delivered", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_FAILED": "Delivery failed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.SHIPPED_ON_ITS_WAY": "On its way", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_APPROVAL": "Pending approval", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.LABEL.WAITING_FOR_PAYMENT": "Payment processing", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN_REJECTED_REASONS": "Return decline reason", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT": "You will be contacted by the courier company or the seller to arrange the pickup of your return. For more information", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.CONTENT.PLANT": "We sent you an e-mail with the instructions to follow to return your item. Please check your e-mail.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.RETURN.VIEW_INSTRUCTIONS": "view return instructions", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.STATUS.VIEW_REASONS": "View reason", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TOTAL": "Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TOTAL_PRICE": "Total price", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK": "Track this item", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK_ID": "Track ID:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACK_PACKAGE": "Track package {numPackage}", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING_ITEM": "Track this item", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.BUTTON": "Tracking not available", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.LABEL": "Why there is no tracking?", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.INFO": "Shipment tracking may not be available for the following reasons:", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_1": "Some shipments do not have tracking information.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_2": "Marketplace sellers do not always provide Metro Markets with tracking information for their orders.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_3": "The freight forwarder still has to pick up the shipment from the seller.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.ITEM_4": "There may be delays in synchronizing data.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.MORE_1": "Do you have further questions? Check out our", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.MORE_2": "FAQs for Delivery & Tracking.", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_LINE.TRACKING.NOT_AVAILABLE.SIDEBAR.TITLE": "Why is there no tracking available?", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.DISCOUNT": "Total Discount", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.GRAND_TOTAL": "Grand Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.SHIPPING_COST": "Shipping cost", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.SUBTOTAL": "Subtotal", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.TITLE": "Summary of the order", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.TOTAL": "Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER_SUMMARY.VAT": "VAT", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.BACK": "Back to Orders", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.DELIVER": "Deliver to", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDER_NUMBER": "Order number", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDER_PLACED": "Orders placed", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDERS": "Orders", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.ORDERS_DETAILS": "Order Details", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.TOTAL": "Total", "PAGES.ACCOUNT.ORDER_HISTORY.ORDER.VIEW_DETAILS": "View Order Details", "PAGES.ACCOUNT.ORDER_HISTORY.PENDING_INFO.CHECK_EMAIL": "Check your email", "PAGES.ACCOUNT.ORDER_HISTORY.PENDING_INFO.PAYMENT_PENDING": "Rate Pay payment pending.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_1.CONTENT": "Prepare the item(s) for a pick-up.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_1.TITLE": "Prepare items", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_2.CONTENT": "Wait for our customer service team to contact you and arrange a pick-up date and time for your return.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_2.TITLE": "Receive pick-up details", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_3.CONTENT": "Handover your return item(s) to the carrier.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_3.TITLE": "Handover items", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_4.CONTENT": "Receive your refund once it gets processed. Visit your orders page to stay updated on the return status.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.ITEM_4.TITLE": "Get refund", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.BULKY.TITLE": "<PERSON><PERSON><PERSON> returns to Metro", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_1.CONTENT": "Pack the item(s) in an appropriate package or the one you received it in.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_1.TITLE": "Pack items", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_2.CONTENT": "Print and attach return label on the package. Use a separate package for each label you received. You can find the return label attached in your email or download it from your orders page.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_2.TITLE": "Attach return label", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_3.CONTENT": "Drop the package(s) at a convenient branch of the carrier assigned to your return.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_3.TITLE": "Drop-off", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_4.CONTENT": "Receive your refund once it gets processed. Visit your orders page to stay updated on the return status.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.ITEM_4.TITLE": "Get refund", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.METRO.PARCEL.TITLE": "<PERSON><PERSON><PERSON> returns to Metro", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.MORE_1": "Do you have further questions? Check out our", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.MORE_2": "FAQs for Returns & Refunds", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_1.CONTENT": "Wait for seller instructions which may take up to 7 working days.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_2.CONTENT": "Print and attach return label on the package. Use a separate package for each label you received. You can download the return label from your orders page once it is available.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_2.TITLE": "Received a return label?", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_3.CONTENT": "Wait for the seller to contact you and arrange a pick-up date and time for your return.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.ITEM_3.TITLE": "Returning a bulky item?", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_1.TITLE": "Receive seller instructions", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_2.CONTENT": "Receive your refund once it gets processed. Visit your orders page to stay updated on the return status.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.ITEM_2.TITLE": "Get refund", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.PARTNERS.TITLE": "Returns to Partners", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_INSTRUCTIONS.SIDEBAR.TITLE": "Return instructions", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.DOWNLOAD": "Download return label", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.INFORMATION": "Your return label will be available as soon as the seller provides it. You will be notified via e-mail.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN_LABEL.VIEW_INSTRUCTIONS": "View return instructions", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL": "Cancel Return", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.CANCEL": "Cancel", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.CONFIRM": "Yes, discard", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.CONTENT": "Discard return request?", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.CANCEL.MODAL.TITLE": "You are going to discard the return request.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.ALL": "All", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.CANCEL": "Cancel", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.CONFIRM": "Return item(s)", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.ARIA_LABEL": "Selected option is", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.ARIA_LABEL.NONE": "none", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.SELECT.QUANTITY": "Select return quantity", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.DROPDOWN.SELECT.RETURN": "Select a reason for return", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.INFO": "Depending on the item, you'll either receive a return label, or the seller will contact you with the next steps.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.INFO.FEE": "A return fee may be deducted from your refund.", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.LEARN_MORE": "Learn more", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.QUANTITY": "Return quantity", "PAGES.ACCOUNT.ORDER_HISTORY.RETURN.MODAL.REASON": "Return reason", "PAGES.ACCOUNT.ORDERS_HISTORY.LOAD_MORE_BUTTON": "Show next orders", "PAGES.ACCOUNT.ORDERS_HISTORY.TITLE": "Orders", "PAGES.AUTH.PAYMENT_LINK.PREPARING.CART.MESSAGE_TEXT": "We are currently preparing your cart.", "PAGES.AUTH.PAYMENT_LINK.WAITING.MESSAGE_TEXT": "This may take a moment…", "REGULAR.ERROR_MESSAGES.MANDATORY": "The field cannot be empty", "TITLE.PAGE.ACCOUNT.APPROVE_ORDERS": "Approval Overview | METRO", "TITLE.PAGE.PAYMENT_LINK": "METRO Marketplace - Payment Link"}